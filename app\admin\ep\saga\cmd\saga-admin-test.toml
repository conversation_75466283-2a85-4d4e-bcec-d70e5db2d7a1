
[bm]
    addr = "0.0.0.0:8000"
    maxListen = 10000
    timeout = "1000s"

[httpClient]
    key = "c05dd4e1638a8af0"
    secret = "7daa7f8c06cd33c5c3067063c746fdcb"
    dial = "2s"
    timeout = "10s"
    keepAlive = "60s"
    timer = 1000
    [httpClient.breaker]
        window  = "10s"
        sleep   = "2000ms"
        bucket  = 10
        ratio   = 0.5
        request = 100

[orm]
    dsn = "root:123456@tcp(172.16.60.47:3306)/saga?timeout=20000ms&readTimeout=20000ms&writeTimeout=20000ms&parseTime=true&loc=Local&charset=utf8,utf8mb4"
    active = 5
    idle = 5
    idleTimeout = "4h"

[memcache]
    mcRecordExpire = "720h"
    [memcache.mc]
        name = "saga-admin"
        proto = "tcp"
        addr = "172.18.33.130:11211"
        idle = 5
        active = 10
        dialTimeout = "1s"
        readTimeout = "1s"
        writeTimeout = "1s"
        idleTimeout = "10s"

[redis]
    active = 100
    idle = 100
    idleTimeout = "3s"
    waitTimeout = "3s"
    wait = false
    name = "redis"
    proto = "tcp"
    addr = "172.22.33.137:6821"
    dialTimeout = "1s"
    readTimeout = "1s"
    writeTimeout = "1s"

[permit]
    [permit.Memcache]
        name = "go-business/auth"
        proto = "tcp"
        addr = "************:11232"
        active = 10
        idle = 10
        dialTimeout = "1s"
        readTimeout = "1s"
        writeTimeout = "1s"
        idleTimeout = "80s"

[property]
    [property.gitlab]
        api = "http://git-test.bilibili.co/api/v4"
        token = ""
    [property.git]
        api = "http://git.bilibili.co/api/v4"
        token = ""
        checkCron = "* */15 * * * ?"
        userlist = ["wuwei"]
            [[property.git.alertpipeline]]
                projectName = "andruid"
                projectID = 5822
                runningtimeout = 60
                runningrate = 50
                runningthreshold = 5
                pendingtimeout = 2
                pendingthreshold = 5
            [[property.git.alertpipeline]]
                projectName = "loktar"
                projectID = 4928
                runningtimeout = 20
                runningrate = 50
                runningthreshold = 3
                pendingtimeout = 10
                pendingthreshold = 5
    [property.syncproject]
        checkCron = "0 0 0 ? * 0"
    [property.syncdata]
        syncalltime = false
        defaultsyncdays = 1
        checkCron = "0 0 22 ? * 4"
        wechatUser = ["wuwei","zhanglin"]
    [property.department]
        label = "主站 直播 bplus 开放平台 创作中心 商业产品 数据中心 视频云 游戏 火鸟"
        value = "mainsite live bplus openplatform creative advertising datacenter videocloud game firebird"
    [property.business]
        label = "android iOS 后端 前端"
        value = "android ios service web"
	[property.defaultproject]
        projectIDs=[682,4928,5822]
        status = ["created","pending","running","failed","success","canceled","skipped","manual"]
        types = [1]
        commitmrtypes = [0,1]
    [property.group]
        name = "android ios live-dev"
        department = "mainsite mainsite live"
        business = "android ios service"
    [property.mail]
        host = "smtp.exmail.qq.com"
        port = 465
        address = "***"
        pwd = "***"
        name = "SAGA"
	[property.wechat]
        appId = 1000047
        appSecret = ""
		redisExpireWechat=3000
    [property.contact]
        appId = 9527
        appSecret = ""

    [property.sven]
        configValue="http://fat1-sven.bilibili.co/x/admin/config/config/value"
        configs="http://fat1-sven.bilibili.co/x/admin/config/config/configs"
        configUpdate="http://fat1-sven.bilibili.co/x/admin/config/home/<USER>/update"
        tagUpdate="http://fat1-sven.bilibili.co/x/admin/config/home/<USER>/update"
        [property.sven.sagaConfigsParam]
            filename = "1254"
            appName="test.ep.saga"
            treeID=56733
            env = "uat"
            zone = "sh001"
            buildID=68
            build="v1.0"
            token = "f6a596d04e69b4e39cf225a8f0748312"
            increment = 1
            force=1
            userList=["wuwei","zhanglin","maojian","shenyue"]



