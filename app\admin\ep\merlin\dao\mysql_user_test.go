package dao

import (
	"testing"
	"time"

	"go-common/app/admin/ep/merlin/model"

	. "github.com/smartystreets/goconvey/convey"
)

var (
	testTime = time.Now().Format("2006_01_02_15_04_05")
	testUser = model.User{
		Name:  testTime,
		EMail: testTime + "@bilibili.com"}
)

func Test_User(t *testing.T) {
	<PERSON><PERSON>("test CreateUser", t, func() {
		err := d.CreateUser(&testUser)
		So(err, ShouldBeNil)
	})

	<PERSON>vey("find user by user name", t, func() {
		userInDb, err := d.FindUserByUserName(testUser.Name)
		So(userInDb.EMail, ShouldEqual, testUser.EMail)
		So(err, ShouldBeNil)
	})

	Convey("find user by id", t, func() {
		userID := testUser.ID
		userInDb, err := d.FindUserByID(userID)
		So(userInDb.EMail, ShouldEqual, testUser.EMail)
		So(err, ShouldBeNil)
	})

	<PERSON><PERSON>("delete user", t, func() {
		err := d.<PERSON>ser(&testUser)
		So(err, ShouldBeNil)
	})

}
