// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: upload.proto

package v1

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "github.com/gogo/protobuf/gogoproto"

import encoding_binary "encoding/binary"

import io "io"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type NewTokenReq struct {
	// 上传到 BFS 的 bucket
	Bucket string `protobuf:"bytes,1,opt,name=bucket,proto3" json:"bucket,omitempty" form:"bucket" validate:"required"`
	// 上传到指定的 BFS 目录（可以用来区分业务）
	Dir string `protobuf:"bytes,2,opt,name=dir,proto3" json:"dir,omitempty" form:"dir"`
	// 操作人（mlive通过dashboard授权获取到的操作人）
	Operator             string   `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty" form:"operator" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewTokenReq) Reset()         { *m = NewTokenReq{} }
func (m *NewTokenReq) String() string { return proto.CompactTextString(m) }
func (*NewTokenReq) ProtoMessage()    {}
func (*NewTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_upload_36f9b26ba548ca45, []int{0}
}
func (m *NewTokenReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *NewTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_NewTokenReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *NewTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewTokenReq.Merge(dst, src)
}
func (m *NewTokenReq) XXX_Size() int {
	return m.Size()
}
func (m *NewTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NewTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_NewTokenReq proto.InternalMessageInfo

func (m *NewTokenReq) GetBucket() string {
	if m != nil {
		return m.Bucket
	}
	return ""
}

func (m *NewTokenReq) GetDir() string {
	if m != nil {
		return m.Dir
	}
	return ""
}

func (m *NewTokenReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type NewTokenResp struct {
	// 授予的 token
	Token                string   `protobuf:"bytes,1,opt,name=token,proto3" json:"token"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewTokenResp) Reset()         { *m = NewTokenResp{} }
func (m *NewTokenResp) String() string { return proto.CompactTextString(m) }
func (*NewTokenResp) ProtoMessage()    {}
func (*NewTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_upload_36f9b26ba548ca45, []int{1}
}
func (m *NewTokenResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *NewTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_NewTokenResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *NewTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewTokenResp.Merge(dst, src)
}
func (m *NewTokenResp) XXX_Size() int {
	return m.Size()
}
func (m *NewTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NewTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_NewTokenResp proto.InternalMessageInfo

func (m *NewTokenResp) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

// The file/data part must use Request.FormFile() to retrieve manually.
type UploadFileReq struct {
	// 上传到 BFS 的 bucket
	Bucket string `protobuf:"bytes,1,opt,name=bucket,proto3" json:"bucket,omitempty" form:"bucket" validate:"required"`
	// 上传到指定的 BFS 目录（可以用来区分业务）
	Dir string `protobuf:"bytes,2,opt,name=dir,proto3" json:"dir,omitempty" form:"dir"`
	// 上传的到bfs的文件名（存储在bfs的文件名，不传bfs会根据文件的sha1值生成并返回）
	Filename string `protobuf:"bytes,3,opt,name=filename,proto3" json:"filename,omitempty" form:"file_name"`
	// 上传的文件的类型（不指定时会自动检测文件类型）
	ContentType string `protobuf:"bytes,4,opt,name=contentType,proto3" json:"contentType,omitempty" form:"content_type"`
	// 图片水印key，添加图片水印需要上传该参数, 新业务需要提前向bfs申请
	WmKey string `protobuf:"bytes,5,opt,name=wmKey,proto3" json:"wmKey,omitempty" form:"wm_key"`
	// 文字水印，限制不超过20个字符
	WmText string `protobuf:"bytes,6,opt,name=wmText,proto3" json:"wmText,omitempty" form:"wm_text"`
	// 水印位置右下角 到原图右下角 水平距离，默认10px
	WmPaddingX uint32 `protobuf:"varint,7,opt,name=wmPaddingX,proto3" json:"wmPaddingX,omitempty" form:"wm_padding_x"`
	// 水印位置右下角 到原图右下角 垂直距离，默认10px
	WmPaddingY uint32 `protobuf:"varint,8,opt,name=wmPaddingY,proto3" json:"wmPaddingY,omitempty" form:"wm_padding_y"`
	// 水印宽度占原图高度的比例(0,1) （只支持按照宽度压缩)，默认值: 0.035
	WmScale float64 `protobuf:"fixed64,9,opt,name=wmScale,proto3" json:"wmScale,omitempty" form:"wm_scale"`
	// 上传 Token，通过 obtainToken 接口获取
	Token                string   `protobuf:"bytes,10,opt,name=token,proto3" json:"token,omitempty" form:"token" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UploadFileReq) Reset()         { *m = UploadFileReq{} }
func (m *UploadFileReq) String() string { return proto.CompactTextString(m) }
func (*UploadFileReq) ProtoMessage()    {}
func (*UploadFileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_upload_36f9b26ba548ca45, []int{2}
}
func (m *UploadFileReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UploadFileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UploadFileReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *UploadFileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadFileReq.Merge(dst, src)
}
func (m *UploadFileReq) XXX_Size() int {
	return m.Size()
}
func (m *UploadFileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadFileReq.DiscardUnknown(m)
}

var xxx_messageInfo_UploadFileReq proto.InternalMessageInfo

func (m *UploadFileReq) GetBucket() string {
	if m != nil {
		return m.Bucket
	}
	return ""
}

func (m *UploadFileReq) GetDir() string {
	if m != nil {
		return m.Dir
	}
	return ""
}

func (m *UploadFileReq) GetFilename() string {
	if m != nil {
		return m.Filename
	}
	return ""
}

func (m *UploadFileReq) GetContentType() string {
	if m != nil {
		return m.ContentType
	}
	return ""
}

func (m *UploadFileReq) GetWmKey() string {
	if m != nil {
		return m.WmKey
	}
	return ""
}

func (m *UploadFileReq) GetWmText() string {
	if m != nil {
		return m.WmText
	}
	return ""
}

func (m *UploadFileReq) GetWmPaddingX() uint32 {
	if m != nil {
		return m.WmPaddingX
	}
	return 0
}

func (m *UploadFileReq) GetWmPaddingY() uint32 {
	if m != nil {
		return m.WmPaddingY
	}
	return 0
}

func (m *UploadFileReq) GetWmScale() float64 {
	if m != nil {
		return m.WmScale
	}
	return 0
}

func (m *UploadFileReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type UploadFileResp struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UploadFileResp) Reset()         { *m = UploadFileResp{} }
func (m *UploadFileResp) String() string { return proto.CompactTextString(m) }
func (*UploadFileResp) ProtoMessage()    {}
func (*UploadFileResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_upload_36f9b26ba548ca45, []int{3}
}
func (m *UploadFileResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UploadFileResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UploadFileResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *UploadFileResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadFileResp.Merge(dst, src)
}
func (m *UploadFileResp) XXX_Size() int {
	return m.Size()
}
func (m *UploadFileResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadFileResp.DiscardUnknown(m)
}

var xxx_messageInfo_UploadFileResp proto.InternalMessageInfo

func (m *UploadFileResp) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func init() {
	proto.RegisterType((*NewTokenReq)(nil), "live.liveadmin.v1.NewTokenReq")
	proto.RegisterType((*NewTokenResp)(nil), "live.liveadmin.v1.NewTokenResp")
	proto.RegisterType((*UploadFileReq)(nil), "live.liveadmin.v1.UploadFileReq")
	proto.RegisterType((*UploadFileResp)(nil), "live.liveadmin.v1.UploadFileResp")
}
func (m *NewTokenReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NewTokenReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Bucket) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUpload(dAtA, i, uint64(len(m.Bucket)))
		i += copy(dAtA[i:], m.Bucket)
	}
	if len(m.Dir) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUpload(dAtA, i, uint64(len(m.Dir)))
		i += copy(dAtA[i:], m.Dir)
	}
	if len(m.Operator) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintUpload(dAtA, i, uint64(len(m.Operator)))
		i += copy(dAtA[i:], m.Operator)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *NewTokenResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NewTokenResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Token) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUpload(dAtA, i, uint64(len(m.Token)))
		i += copy(dAtA[i:], m.Token)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *UploadFileReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UploadFileReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Bucket) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUpload(dAtA, i, uint64(len(m.Bucket)))
		i += copy(dAtA[i:], m.Bucket)
	}
	if len(m.Dir) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUpload(dAtA, i, uint64(len(m.Dir)))
		i += copy(dAtA[i:], m.Dir)
	}
	if len(m.Filename) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintUpload(dAtA, i, uint64(len(m.Filename)))
		i += copy(dAtA[i:], m.Filename)
	}
	if len(m.ContentType) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintUpload(dAtA, i, uint64(len(m.ContentType)))
		i += copy(dAtA[i:], m.ContentType)
	}
	if len(m.WmKey) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintUpload(dAtA, i, uint64(len(m.WmKey)))
		i += copy(dAtA[i:], m.WmKey)
	}
	if len(m.WmText) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintUpload(dAtA, i, uint64(len(m.WmText)))
		i += copy(dAtA[i:], m.WmText)
	}
	if m.WmPaddingX != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintUpload(dAtA, i, uint64(m.WmPaddingX))
	}
	if m.WmPaddingY != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintUpload(dAtA, i, uint64(m.WmPaddingY))
	}
	if m.WmScale != 0 {
		dAtA[i] = 0x49
		i++
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.WmScale))))
		i += 8
	}
	if len(m.Token) > 0 {
		dAtA[i] = 0x52
		i++
		i = encodeVarintUpload(dAtA, i, uint64(len(m.Token)))
		i += copy(dAtA[i:], m.Token)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *UploadFileResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UploadFileResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Url) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUpload(dAtA, i, uint64(len(m.Url)))
		i += copy(dAtA[i:], m.Url)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func encodeVarintUpload(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *NewTokenReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Bucket)
	if l > 0 {
		n += 1 + l + sovUpload(uint64(l))
	}
	l = len(m.Dir)
	if l > 0 {
		n += 1 + l + sovUpload(uint64(l))
	}
	l = len(m.Operator)
	if l > 0 {
		n += 1 + l + sovUpload(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *NewTokenResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Token)
	if l > 0 {
		n += 1 + l + sovUpload(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *UploadFileReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Bucket)
	if l > 0 {
		n += 1 + l + sovUpload(uint64(l))
	}
	l = len(m.Dir)
	if l > 0 {
		n += 1 + l + sovUpload(uint64(l))
	}
	l = len(m.Filename)
	if l > 0 {
		n += 1 + l + sovUpload(uint64(l))
	}
	l = len(m.ContentType)
	if l > 0 {
		n += 1 + l + sovUpload(uint64(l))
	}
	l = len(m.WmKey)
	if l > 0 {
		n += 1 + l + sovUpload(uint64(l))
	}
	l = len(m.WmText)
	if l > 0 {
		n += 1 + l + sovUpload(uint64(l))
	}
	if m.WmPaddingX != 0 {
		n += 1 + sovUpload(uint64(m.WmPaddingX))
	}
	if m.WmPaddingY != 0 {
		n += 1 + sovUpload(uint64(m.WmPaddingY))
	}
	if m.WmScale != 0 {
		n += 9
	}
	l = len(m.Token)
	if l > 0 {
		n += 1 + l + sovUpload(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *UploadFileResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Url)
	if l > 0 {
		n += 1 + l + sovUpload(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovUpload(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozUpload(x uint64) (n int) {
	return sovUpload(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *NewTokenReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUpload
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: NewTokenReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: NewTokenReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Bucket", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUpload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUpload
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Bucket = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dir", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUpload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUpload
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Dir = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Operator", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUpload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUpload
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Operator = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUpload(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUpload
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *NewTokenResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUpload
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: NewTokenResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: NewTokenResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Token", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUpload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUpload
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Token = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUpload(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUpload
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UploadFileReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUpload
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UploadFileReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UploadFileReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Bucket", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUpload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUpload
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Bucket = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dir", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUpload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUpload
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Dir = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Filename", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUpload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUpload
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Filename = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ContentType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUpload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUpload
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ContentType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field WmKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUpload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUpload
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.WmKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field WmText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUpload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUpload
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.WmText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field WmPaddingX", wireType)
			}
			m.WmPaddingX = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUpload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.WmPaddingX |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field WmPaddingY", wireType)
			}
			m.WmPaddingY = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUpload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.WmPaddingY |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field WmScale", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.WmScale = float64(math.Float64frombits(v))
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Token", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUpload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUpload
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Token = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUpload(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUpload
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UploadFileResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUpload
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UploadFileResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UploadFileResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUpload
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUpload
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUpload(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUpload
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipUpload(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowUpload
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUpload
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUpload
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthUpload
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowUpload
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipUpload(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthUpload = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowUpload   = fmt.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("upload.proto", fileDescriptor_upload_36f9b26ba548ca45) }

var fileDescriptor_upload_36f9b26ba548ca45 = []byte{
	// 547 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x93, 0xc1, 0x6e, 0xd3, 0x30,
	0x18, 0xc7, 0x95, 0x75, 0xed, 0xd6, 0x6f, 0x6b, 0xd9, 0x3c, 0x24, 0x42, 0x0f, 0x75, 0x66, 0x40,
	0xab, 0x40, 0x4b, 0xd9, 0x38, 0x20, 0x2a, 0x71, 0x29, 0xd2, 0x2e, 0x93, 0x00, 0x85, 0x4e, 0x62,
	0x5c, 0xaa, 0xb4, 0x71, 0x4b, 0xd4, 0x24, 0xce, 0x52, 0xa7, 0x69, 0x9f, 0x82, 0x87, 0xe1, 0x25,
	0x38, 0xf2, 0x04, 0x16, 0xea, 0x71, 0xc7, 0x3c, 0x01, 0x8a, 0x9d, 0x96, 0x4c, 0x54, 0xe3, 0xc6,
	0xc5, 0xed, 0xe7, 0xff, 0xef, 0x57, 0xdb, 0xf5, 0x67, 0xd8, 0x8f, 0x43, 0x8f, 0xd9, 0x8e, 0x19,
	0x46, 0x8c, 0x33, 0x74, 0xe8, 0xb9, 0x33, 0x6a, 0x66, 0x83, 0xed, 0xf8, 0x6e, 0x60, 0xce, 0xce,
	0x1a, 0xa7, 0x63, 0x97, 0x7f, 0x8d, 0x07, 0xe6, 0x90, 0xf9, 0xed, 0x31, 0x1b, 0xb3, 0xb6, 0x24,
	0x07, 0xf1, 0x48, 0x56, 0xb2, 0x90, 0xdf, 0xd4, 0x2f, 0x90, 0xef, 0x1a, 0xec, 0xbd, 0xa7, 0x49,
	0x8f, 0x4d, 0x68, 0x60, 0xd1, 0x1b, 0xf4, 0x16, 0x2a, 0x83, 0x78, 0x38, 0xa1, 0x5c, 0xd7, 0x0c,
	0xad, 0x55, 0xed, 0x3e, 0x4b, 0x05, 0x3e, 0x1e, 0xb1, 0xc8, 0xef, 0x10, 0x35, 0x4f, 0x8c, 0x99,
	0xed, 0xb9, 0x8e, 0xcd, 0x69, 0x87, 0x44, 0xf4, 0x26, 0x76, 0x23, 0xea, 0x10, 0x2b, 0x97, 0x90,
	0x01, 0x25, 0xc7, 0x8d, 0xf4, 0x2d, 0xe9, 0xd6, 0x53, 0x81, 0x41, 0xb9, 0x8e, 0x1b, 0x11, 0x2b,
	0x8b, 0xd0, 0x3b, 0xd8, 0x65, 0x21, 0x8d, 0x6c, 0xce, 0x22, 0xbd, 0x24, 0xb1, 0x93, 0x54, 0xe0,
	0x27, 0x0a, 0x5b, 0x25, 0x9b, 0x17, 0x59, 0x8b, 0xa4, 0x0d, 0xfb, 0x7f, 0x36, 0x3d, 0x0d, 0x11,
	0x86, 0x32, 0xcf, 0x8a, 0x7c, 0xd3, 0xd5, 0x5b, 0x81, 0xd5, 0x84, 0xa5, 0x3e, 0xc8, 0xb7, 0x6d,
	0xa8, 0x5d, 0xc9, 0x7f, 0xee, 0xc2, 0xf5, 0xe8, 0x7f, 0x39, 0xe8, 0x4b, 0xd8, 0x1d, 0xb9, 0x1e,
	0x0d, 0x6c, 0x9f, 0xe6, 0x07, 0x7d, 0x98, 0x0a, 0x7c, 0xa0, 0xb0, 0x2c, 0xe9, 0x67, 0x11, 0xb1,
	0xd6, 0x14, 0x7a, 0x03, 0x7b, 0x43, 0x16, 0x70, 0x1a, 0xf0, 0xde, 0x22, 0xa4, 0xfa, 0xb6, 0x94,
	0x1e, 0xa5, 0x02, 0x1f, 0x29, 0x29, 0x0f, 0xfb, 0x7c, 0x11, 0x52, 0x62, 0x15, 0x59, 0x74, 0x02,
	0xe5, 0xc4, 0xbf, 0xa4, 0x0b, 0xbd, 0x2c, 0xa5, 0xc3, 0x54, 0xe0, 0x9a, 0x92, 0x12, 0xbf, 0x3f,
	0xa1, 0x0b, 0x62, 0xa9, 0x1c, 0x3d, 0x87, 0x4a, 0xe2, 0xf7, 0xe8, 0x9c, 0xeb, 0x15, 0x49, 0xa2,
	0x54, 0xe0, 0xfa, 0x9a, 0xe4, 0x74, 0xce, 0x89, 0x95, 0x13, 0xe8, 0x35, 0x40, 0xe2, 0x7f, 0xb4,
	0x1d, 0xc7, 0x0d, 0xc6, 0x9f, 0xf5, 0x1d, 0x43, 0x6b, 0xd5, 0x8a, 0xdb, 0x49, 0xfc, 0x7e, 0xa8,
	0xc2, 0xfe, 0x9c, 0x58, 0x05, 0xf4, 0x8e, 0x78, 0xad, 0xef, 0xde, 0x23, 0x2e, 0x8a, 0xe2, 0x35,
	0x3a, 0x85, 0x9d, 0xc4, 0xff, 0x34, 0xb4, 0x3d, 0xaa, 0x57, 0x0d, 0xad, 0xa5, 0x75, 0x8f, 0x52,
	0x81, 0x1f, 0xac, 0xad, 0x69, 0x96, 0x10, 0x6b, 0xc5, 0xa0, 0xce, 0xea, 0xda, 0x41, 0x9e, 0xe5,
	0x69, 0x2a, 0xb0, 0xa1, 0x60, 0x75, 0xeb, 0x1b, 0x6f, 0x30, 0xef, 0x88, 0x17, 0x50, 0x2f, 0x36,
	0xc4, 0x34, 0x44, 0x8f, 0xa1, 0x14, 0x47, 0x5e, 0xde, 0x0e, 0x3b, 0xb7, 0x02, 0x67, 0xa5, 0x95,
	0x0d, 0xe7, 0x1f, 0xa0, 0x2c, 0x9b, 0x0d, 0x5d, 0x40, 0x29, 0xa0, 0x09, 0x6a, 0x9a, 0x7f, 0x3d,
	0x3c, 0xb3, 0xf0, 0x8a, 0x1a, 0xf8, 0xde, 0x7c, 0x1a, 0x9e, 0x5f, 0x41, 0x45, 0xad, 0x8e, 0x2e,
	0x61, 0x3b, 0x6b, 0x00, 0x64, 0x6c, 0x50, 0xee, 0x74, 0x6c, 0xe3, 0xf8, 0x1f, 0xc4, 0x34, 0xec,
	0x1e, 0xfc, 0x58, 0x36, 0xb5, 0x9f, 0xcb, 0xa6, 0xf6, 0x6b, 0xd9, 0xd4, 0xbe, 0x6c, 0xcd, 0xce,
	0x06, 0x15, 0xf9, 0xcc, 0x5f, 0xfd, 0x0e, 0x00, 0x00, 0xff, 0xff, 0x66, 0xef, 0xbe, 0x83, 0x38,
	0x04, 0x00, 0x00,
}
