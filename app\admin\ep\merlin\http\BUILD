package(default_visibility = ["//visibility:public"])

load(
    "@io_bazel_rules_go//go:def.bzl",
    "go_library",
)

go_library(
    name = "go_default_library",
    srcs = [
        "audit.go",
        "bilihub.go",
        "cluster.go",
        "dashboard.go",
        "devicefarm.go",
        "http.go",
        "image.go",
        "machine.go",
        "machineV2.go",
        "node.go",
        "tree.go",
        "user.go",
    ],
    importpath = "go-common/app/admin/ep/merlin/http",
    tags = ["automanaged"],
    visibility = ["//visibility:public"],
    deps = [
        "//app/admin/ep/merlin/conf:go_default_library",
        "//app/admin/ep/merlin/model:go_default_library",
        "//app/admin/ep/merlin/service:go_default_library",
        "//library/ecode:go_default_library",
        "//library/log:go_default_library",
        "//library/net/http/blademaster:go_default_library",
        "//library/net/http/blademaster/binding:go_default_library",
        "//library/net/http/blademaster/middleware/permit:go_default_library",
    ],
)

filegroup(
    name = "package-srcs",
    srcs = glob(["**"]),
    tags = ["automanaged"],
    visibility = ["//visibility:private"],
)

filegroup(
    name = "all-srcs",
    srcs = [":package-srcs"],
    tags = ["automanaged"],
    visibility = ["//visibility:public"],
)
