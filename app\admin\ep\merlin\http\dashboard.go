package http

import (
	bm "go-common/library/net/http/blademaster"
)

func queryMachineLifeCycle(c *bm.Context) {
	c.<PERSON><PERSON><PERSON>(svc.QueryMachineLifeCycle(c))
}

func queryMachineCount(c *bm.Context) {
	c.J<PERSON><PERSON>(svc.QueryMachineCount(c))
}

func queryMachineTime(c *bm.Context) {
	c.JSO<PERSON>(svc.QueryMachineCreatedAndEndTime(c))
}

func queryMachineUsage(c *bm.Context) {
	c.JSON(svc.QueryMachineUsage(c))
}

func queryMobileMachineUsageCount(c *bm.Context) {
	c.JSO<PERSON>(svc.QueryMobileMachineUsageCount(c))
}

func queryMobileMachineModeCount(c *bm.Context) {
	c.J<PERSON><PERSON>(svc.QueryMobileMachineModeCount(c))
}

func queryMobileMachineUsageTime(c *bm.Context) {
	c.<PERSON><PERSON><PERSON>(svc.QueryMobileMachineUsageTime(c))
}
