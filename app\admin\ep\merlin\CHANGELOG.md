##### ep-merlin

##### Version 1.9.2
1.修改手动延期bug

##### Version 1.9.1
1.创建非快照机器 保存镜像名称

##### Version 1.8.9
1.不支持uat创建虚拟机

##### Version 1.8.8
1.创建中机器无法删除

##### Version 1.8.7
1.删除二进制文件

##### Version 1.8.6
1.镜像过滤size为0，同时排序

##### Version 1.8.5
1.对接paas，支持超配比配置

##### Version 1.8.4
1.移除配置文件mima

##### Version 1.8.3
1.增加dashboard接口

##### Version 1.8.2
1.创建机器接口更新

##### Version 1.8.1
1. 邮件收件人不存在 ，会通知noticeOwner
2. 轮询快照记录状态

##### Version 1.8.0
1. 增加设备农场借出归还流程
2. 虚拟机支持转移他人


##### Version 1.7.2
1. 配置文件去除密码

##### Version 1.7
1 添加镜像专属chan，并增加多任务处理
2 增加微信通知
3 修改配置文件

##### Version 1.6
1 支持带快照的虚拟机 制作成镜像
2 创建机器镜像选择列表对接hub

##### Version 1.5.5.3
1 快照功能调优

##### Version 1.5.5
1 对接DeviceFarm，机器轮询更新机制优化
2 对接DeviceFarm前端和修改接口


##### Version 1.5.1
1 对接DeviceFarm，机器轮询更新机制加强
2 对接DeviceFarm前端，修改接口
3 增加主动snapshot功能


##### Version 1.5.0
1 对接hub，支持镜像查询，删除等
2 支持docker api操作，下载，retag和上传镜像


##### Version 1.4.0
1 日志列表，增加type类型，移动设备日志也存该该表
2 对接DeviceFarm， 管理移动设备，支持查询，绑定，解绑，开机，关机


##### Version 1.3.0
1 机器列表，默认按用户名查询
2 日志列表，支持多条件查询
3 删除别人名下机器后，会邮寄通知双方
4 机器列表，按机器名模糊查询


##### Version 1.2.0
1. 创建机器支持关联至服务树多节点
2. 创建机器支持配置启动命令, hosts, 环境变量参数
3. 机器的节点支持管理(新增, 删除, 更新)
4. 修改机器查询接口支持从多节点中过滤
5. 创建机器外接硬盘支持填写挂载目录
6. 修复节点树应该小于1 d


##### Version 1. 1. 0
1.  Merlin 二期添加审批流程  
2.  新增status 状态查询
3.  新增定时任务管理创建过久机器
4.  修改status取值范围 
5.  修复machine log 总数错误问题
6.  修改数据库字段名定义, 将大写变为小写
7.  修复查询机器所有人都能看到创建失败机器bug
8.  增加镜像逻辑删除功能
9.  在新建机器之前, 验证当前是否包含containers子节点
10.  支持查询所有机器时, 返回机器 ip 地址  

##### Version 1. 0. 1
1.  修改image的update_time  
2.  增加定时任务验证机器是否创建成功  
3.  新增删除机器Hook
4.  修改UpdateTime为Utime, CreateTime为Ctime
5.  新增机器POD_NAME字段
6. 修改机器内存的单位为: G  
7. 将DelMachine 修改为支持中间件方法  
8. 修改定时任务, 采用服务树接口确定状态    

##### Version 1. 0. 0
1. 机器申请  
2. 机器销毁  
3. 修改 business/ecode 目录到 ecode 目录下面  
4. 添加ping测试接口  
5. 修改分页参数和删除 machine 前缀配置  
6. 修改数据库返回, 改成使用指针  
7. 修复数据库时间为空问题  
8. 修复 treeId 层级错误问题  
9. 修复查询机器中 username 需要模糊匹配问题  
10. machine log 查询接口返回为中文  