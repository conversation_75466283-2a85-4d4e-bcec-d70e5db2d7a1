# This is a TOML document. Boom

version = "1.0.0"
user = "nobody"
pid = "/tmp/merlin.pid"
dir = "./"
perf = "0.0.0.0:6420"

[log]
dir = "/data/merlin/log"

[bm]
 [bm.inner]
 addr = "0.0.0.0:9001"
 maxListen = 10
 timeout = "1s"
 [bm.local]
 addr = "0.0.0.0:9002"
 maxListen = 10
 timeout = "1s"

[httpClient]
    key = "c05dd4e1638a8af0"
    secret = "7daa7f8c06cd33c5c3067063c746fdcb"
    dial = "2s"
    timeout = "10s"
    keepAlive = "60s"
    timer = 1000
    [httpClient.breaker]
    window  = "10s"
    sleep   = "2000ms"
    bucket  = 10
    ratio   = 0.5
    request = 100

[identify]
 whiteAccessKey = ""
 whiteMid = 0
 [identify.app]
 key = "6a29f8ed87407c11"
 secret = "d3c5a85f5b895a03735b5d20a273bc57"
 [identify.memcache]
 name = "go-business/identify"
 proto = "tcp"
 addr = "************:11211"
 active = 5
 idle = 1
 dialTimeout = "2s"
 readTimeout = "2s"
 writeTimeout = "2s"
 idleTimeout = "80s"
 [identify.host]
 auth = "http://passport.bilibili.com"
 secret = "http://open.bilibili.com"
 [identify.httpClient]
 key = "f022126a8a365e20"
 secret = "b7b86838145d634b487e67b811b8fab2"
 dial = "3000ms"
 timeout = "2000ms"
 keepAlive = "60s"
 [identify.httpClient.breaker]
 window  = "10s"
 sleep   = "100ms"
 bucket  = 10
 ratio   = 0.5
 request = 100
 [identify.httpClient.url]
 "http://passport.bilibili.co/intranet/auth/tokenInfo" = {timeout = "100ms"}
 "http://passport.bilibili.co/intranet/auth/cookieInfo" = {timeout = "100ms"}
 "http://open.bilibili.co/api/getsecret" = {timeout = "500ms"}

[ecode]
 domain = "uat-api.bilibili.co"
 all = "1h"
 diff = "5m"
 [ecode.clientconfig]
  key = "c1a1cb2d89c33794"
  secret = "dda47eeca111e03e6845017505baea13"
  dial      = "2000ms"
  timeout   = "2s"
  keepAlive = "10s"
  timer     = 128
  [ecode.clientconfig.breaker]
   window  ="3s"
   sleep   ="100ms"
   bucket  = 10
   ratio   = 0.5
   request = 100

[managerAuth]
servicetreeHost = "http://easyst.bilibili.co"
dashboardHost = "http://dashboard-mng.bilibili.co"
dashboardCaller =  "merlin"
    [managerAuth.dsHTTPClient]
    key = "merlin"
    secret = "4fb521f66dfd5efcf6e77d078ed2eb0a"
    dial = "2s"
    timeout = "3s"
    keepAlive = "60s"
    timer = 1000
    [managerAuth.dsHTTPClient.breaker]
    window  ="3s"
    sleep   ="200ms"
    bucket  = 10
    ratio   = 0.5
    request = 100
    [managerAuth.stHTTPClient]
     key = "merlin"
        secret = "4fb521f66dfd5efcf6e77d078ed2eb0a"
        dial = "2s"
        timeout = "3s"
        keepAlive = "60s"
        timer = 1000
        [managerAuth.stHTTPClient.breaker]
            window  ="3s"
            sleep   ="100ms"
            bucket  = 10
            ratio   = 0.5
            request = 100


[paas]
host = "http://************"
token = "ngriEorfjzbdyyQ2cdqMYcNxovazPD6Cbte"
machineTimeout = "CURRENT_TIMESTAMP - INTERVAL 20 MINUTE"

[serviceTree]
host = "http://easyst.bilibili.co"
key = "merlin"
secret = "rhRp[Lfnfrp9Jypr7aaJMGn8NC.[E+Gvb9&nRazs6Mm{fEW98.z9yzV*phu)U97#"

[mail]
host = "smtp.exmail.qq.com"
port = 465
username = "<EMAIL>"
password = ""

[Scheduler]
#每天晚上23点查找第二天将要过期机器放入任务表
GetExpiredMachinesTime = "0 0 23 * * ?"

#每天早上10点查找一周后要过期的机器，并发送邮件
#SendTaskMailMachinesWillExpiredTime = "*/15 * * * * ?"
SendTaskMailMachinesWillExpiredTime = "0 0 10 * * ?"

#每隔五分钟删除机器
DeleteExpiredMachinesInTask = "0 */5 * * * ?"

#每隔二十分钟检测机器状态
CheckMachinesStatusInTask = "0 */20 * * * ?"

[orm]
dsn = "root:123456@tcp(*************:3306)/merlin?timeout=2000ms&readTimeout=2000ms&writeTimeout=2000ms&parseTime=true&loc=Local&charset=utf8,utf8mb4"
active = 5
idle = 5
idleTimeout = "4h"

[memcache]
	name = "merlin"
	proto = "tcp"
	addr = "************:11211"
	idle = 5
	active = 10
	dialTimeout = "1s"
	readTimeout = "1s"
	writeTimeout = "1s"
	idleTimeout = "10s"
	expire = "12h"