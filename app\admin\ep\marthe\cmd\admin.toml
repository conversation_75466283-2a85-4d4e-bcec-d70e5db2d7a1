
[bm]
 addr = "0.0.0.0:9001"
 timeout = "10s"

[httpClient]
    key = "c05dd4e1638a8af0"
    secret = "7daa7f8c06cd33c5c3067063c746fdcb"
    dial = "2s"
    timeout = "100s"
    keepAlive = "60s"
    timer = 1000
    [httpClient.breaker]
    window  = "10s"
    sleep   = "2000ms"
    bucket  = 10
    ratio   = 0.5
    request = 100

[mail]
host = "smtp.exmail.qq.com"
port = 465
username = "<EMAIL>"
password = ""
noticeOwner = ["<EMAIL>"]

[memcache]
	name = "merlin"
	proto = "tcp"
	addr = "************:11232"
	idle = 5
	active = 10
	dialTimeout = "1s"
	readTimeout = "1s"
	writeTimeout = "1s"
	idleTimeout = "10s"
	expire = "12h"

[bugly]
#bugly URL
host = "https://bugly.qq.com"

#bugly请求尝试次数
urlRetryCount = 3

#cookie 使用上限次数
cookieUsageUpper = 300

#bugly 每页获取issue个数
issuePageSize = 50

#bugly 抓取issue上限个数
issueCountUpper = 100

#bugly 超级管理员
superOwner = ["fengyifeng","yuanmin"]

[tapd]
#tapd bug相关操作 是否认证tapd权限
bugOperateAuth = false


[Scheduler]
#每过30分钟 跑enable version 抓bugly数据
batchRunEnableVersion =  "0 */30 * * * ?"

#每天晚上23点 定时更新tapd bug
batchRunUpdateTapdBug = "0 0 23 * * ?"

#每10分钟，定时删除超过三小时为执行完毕的任务
disableBatchRunOverTime =  "0 */10 * * * ?"
#过期时限
batchRunOverHourTime = 3

#每天晚上22点，更新同步wechat contact
syncWechatContact = "0 0 22 * * ?"

#是否开启定时任务
active = false


[orm]
dsn = "root:123456@tcp(*************:3306)/Marthe2?timeout=200ms&readTimeout=2000ms&writeTimeout=2000ms&parseTime=true&loc=Local&charset=utf8,utf8mb4"
active = 5
idle = 5
idleTimeout = "4h"

[auth]
    managerHost = "http://uat-manager.bilibili.co"
    dashboardHost = "http://dashboard-mng.bilibili.co"
    dashboardCaller = "marthe"
    [auth.DsHTTPClient]
    key = "marthe"
    secret = "4344bea3587b383bc0b9de5de0efcc3f"
    dial = "1s"
    timeout = "1s"
    keepAlive = "60s"
    [auth.DsHTTPClient.breaker]
    window  = "3s"
    sleep   = "100ms"
    bucket  = 10
    ratio   = 0.5
    request = 100
    [auth.MaHTTPClient]
    key = "f6433799dbd88751"
    secret = "36f8ddb1806207fe07013ab6a77a3935"
    dial = "1ms"
    timeout = "1ms"
    keepAlive = "60s"
    [auth.MaHTTPClient.breaker]
    window  = "3s"
    sleep   = "100ms"
    bucket  = 10
    ratio   = 0.5
    request = 100
    [auth.session]
    sessionIDLength = 32
    cookieLifeTime = 1
    cookieName = "mng-go"
    domain = ".bilibili.co"
    [auth.session.Memcache]
    name = "go-business/auth"
    proto = "tcp"
    addr = "************:11232"
    active = 10
    idle = 10
    dialTimeout = "1ms"
    readTimeout = "1ms"
    writeTimeout = "1ms"
    idleTimeout = "80s"