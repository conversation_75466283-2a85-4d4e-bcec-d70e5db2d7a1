// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: resource.proto

package v1

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "github.com/gogo/protobuf/gogoproto"

import io "io"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// addReq
type AddReq struct {
	Platform             string   `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty" form:"platform" validate:"required"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty" form:"title" validate:"required"`
	JumpPath             string   `protobuf:"bytes,3,opt,name=jumpPath,proto3" json:"jumpPath,omitempty" form:"jumpPath"`
	JumpTime             int64    `protobuf:"varint,4,opt,name=jumpTime,proto3" json:"jumpTime,omitempty" form:"jumpTime"`
	Type                 string   `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty" form:"type" validate:"required"`
	Device               string   `protobuf:"bytes,6,opt,name=device,proto3" json:"device,omitempty" form:"device" validate:"required"`
	StartTime            string   `protobuf:"bytes,7,opt,name=startTime,proto3" json:"startTime,omitempty" form:"startTime" validate:"required"`
	EndTime              string   `protobuf:"bytes,8,opt,name=endTime,proto3" json:"endTime,omitempty" form:"endTime" validate:"required"`
	ImageUrl             string   `protobuf:"bytes,9,opt,name=imageUrl,proto3" json:"imageUrl,omitempty" form:"imageUrl" validate:"required"`
	JumpPathType         int64    `protobuf:"varint,10,opt,name=jumpPathType,proto3" json:"jumpPathType,omitempty" form:"jumpPathType"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddReq) Reset()         { *m = AddReq{} }
func (m *AddReq) String() string { return proto.CompactTextString(m) }
func (*AddReq) ProtoMessage()    {}
func (*AddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{0}
}
func (m *AddReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AddReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *AddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddReq.Merge(dst, src)
}
func (m *AddReq) XXX_Size() int {
	return m.Size()
}
func (m *AddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddReq proto.InternalMessageInfo

func (m *AddReq) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

func (m *AddReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *AddReq) GetJumpPath() string {
	if m != nil {
		return m.JumpPath
	}
	return ""
}

func (m *AddReq) GetJumpTime() int64 {
	if m != nil {
		return m.JumpTime
	}
	return 0
}

func (m *AddReq) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *AddReq) GetDevice() string {
	if m != nil {
		return m.Device
	}
	return ""
}

func (m *AddReq) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *AddReq) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *AddReq) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *AddReq) GetJumpPathType() int64 {
	if m != nil {
		return m.JumpPathType
	}
	return 0
}

// addResp
type AddResp struct {
	Id                   []int64  `protobuf:"varint,1,rep,packed,name=id" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddResp) Reset()         { *m = AddResp{} }
func (m *AddResp) String() string { return proto.CompactTextString(m) }
func (*AddResp) ProtoMessage()    {}
func (*AddResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{1}
}
func (m *AddResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AddResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AddResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *AddResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddResp.Merge(dst, src)
}
func (m *AddResp) XXX_Size() int {
	return m.Size()
}
func (m *AddResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddResp proto.InternalMessageInfo

func (m *AddResp) GetId() []int64 {
	if m != nil {
		return m.Id
	}
	return nil
}

// editReq
type EditReq struct {
	Platform             string   `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty" form:"platform" validate:"required"`
	Id                   int64    `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty" form:"id" validate:"required"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" form:"title"`
	JumpPath             string   `protobuf:"bytes,4,opt,name=jumpPath,proto3" json:"jumpPath,omitempty" form:"jumpPath"`
	JumpTime             int64    `protobuf:"varint,5,opt,name=jumpTime,proto3" json:"jumpTime,omitempty" form:"jumpTime"`
	StartTime            string   `protobuf:"bytes,7,opt,name=startTime,proto3" json:"startTime,omitempty" form:"startTime"`
	EndTime              string   `protobuf:"bytes,8,opt,name=endTime,proto3" json:"endTime,omitempty" form:"endTime"`
	ImageUrl             string   `protobuf:"bytes,9,opt,name=imageUrl,proto3" json:"imageUrl,omitempty" form:"imageUrl"`
	JumpPathType         int64    `protobuf:"varint,10,opt,name=jumpPathType,proto3" json:"jumpPathType,omitempty" form:"jumpPathType"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EditReq) Reset()         { *m = EditReq{} }
func (m *EditReq) String() string { return proto.CompactTextString(m) }
func (*EditReq) ProtoMessage()    {}
func (*EditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{2}
}
func (m *EditReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_EditReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *EditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditReq.Merge(dst, src)
}
func (m *EditReq) XXX_Size() int {
	return m.Size()
}
func (m *EditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EditReq.DiscardUnknown(m)
}

var xxx_messageInfo_EditReq proto.InternalMessageInfo

func (m *EditReq) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

func (m *EditReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *EditReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *EditReq) GetJumpPath() string {
	if m != nil {
		return m.JumpPath
	}
	return ""
}

func (m *EditReq) GetJumpTime() int64 {
	if m != nil {
		return m.JumpTime
	}
	return 0
}

func (m *EditReq) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *EditReq) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *EditReq) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *EditReq) GetJumpPathType() int64 {
	if m != nil {
		return m.JumpPathType
	}
	return 0
}

// editResp
type EditResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EditResp) Reset()         { *m = EditResp{} }
func (m *EditResp) String() string { return proto.CompactTextString(m) }
func (*EditResp) ProtoMessage()    {}
func (*EditResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{3}
}
func (m *EditResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EditResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_EditResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *EditResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditResp.Merge(dst, src)
}
func (m *EditResp) XXX_Size() int {
	return m.Size()
}
func (m *EditResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EditResp.DiscardUnknown(m)
}

var xxx_messageInfo_EditResp proto.InternalMessageInfo

// 下线闪屏
type OfflineReq struct {
	Platform             string   `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty" form:"platform" validate:"required"`
	Id                   int64    `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty" form:"id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OfflineReq) Reset()         { *m = OfflineReq{} }
func (m *OfflineReq) String() string { return proto.CompactTextString(m) }
func (*OfflineReq) ProtoMessage()    {}
func (*OfflineReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{4}
}
func (m *OfflineReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *OfflineReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_OfflineReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *OfflineReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfflineReq.Merge(dst, src)
}
func (m *OfflineReq) XXX_Size() int {
	return m.Size()
}
func (m *OfflineReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OfflineReq.DiscardUnknown(m)
}

var xxx_messageInfo_OfflineReq proto.InternalMessageInfo

func (m *OfflineReq) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

func (m *OfflineReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

type OfflineResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OfflineResp) Reset()         { *m = OfflineResp{} }
func (m *OfflineResp) String() string { return proto.CompactTextString(m) }
func (*OfflineResp) ProtoMessage()    {}
func (*OfflineResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{5}
}
func (m *OfflineResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *OfflineResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_OfflineResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *OfflineResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfflineResp.Merge(dst, src)
}
func (m *OfflineResp) XXX_Size() int {
	return m.Size()
}
func (m *OfflineResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OfflineResp.DiscardUnknown(m)
}

var xxx_messageInfo_OfflineResp proto.InternalMessageInfo

// 获取闪屏配置列表
type GetListReq struct {
	Platform             string   `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty" form:"platform" validate:"required"`
	Page                 int64    `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty" form:"page"`
	PageSize             int64    `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty" form:"pageSize"`
	Type                 string   `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty" form:"type" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetListReq) Reset()         { *m = GetListReq{} }
func (m *GetListReq) String() string { return proto.CompactTextString(m) }
func (*GetListReq) ProtoMessage()    {}
func (*GetListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{6}
}
func (m *GetListReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetListReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetListReq.Merge(dst, src)
}
func (m *GetListReq) XXX_Size() int {
	return m.Size()
}
func (m *GetListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetListReq proto.InternalMessageInfo

func (m *GetListReq) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

func (m *GetListReq) GetPage() int64 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetListReq) GetPageSize() int64 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetListReq) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

type GetListResp struct {
	CurrentPage          int64               `protobuf:"varint,1,opt,name=currentPage,proto3" json:"currentPage"`
	TotalCount           int64               `protobuf:"varint,2,opt,name=totalCount,proto3" json:"totalCount"`
	List                 []*GetListResp_List `protobuf:"bytes,3,rep,name=list" json:"list"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetListResp) Reset()         { *m = GetListResp{} }
func (m *GetListResp) String() string { return proto.CompactTextString(m) }
func (*GetListResp) ProtoMessage()    {}
func (*GetListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{7}
}
func (m *GetListResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetListResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetListResp.Merge(dst, src)
}
func (m *GetListResp) XXX_Size() int {
	return m.Size()
}
func (m *GetListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetListResp proto.InternalMessageInfo

func (m *GetListResp) GetCurrentPage() int64 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *GetListResp) GetTotalCount() int64 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *GetListResp) GetList() []*GetListResp_List {
	if m != nil {
		return m.List
	}
	return nil
}

type GetListResp_List struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	JumpPath             string   `protobuf:"bytes,3,opt,name=jumpPath,proto3" json:"jumpPath"`
	DevicePlatform       string   `protobuf:"bytes,4,opt,name=device_platform,json=devicePlatform,proto3" json:"device_platform"`
	DeviceBuild          int64    `protobuf:"varint,5,opt,name=device_build,json=deviceBuild,proto3" json:"device_build"`
	StartTime            string   `protobuf:"bytes,6,opt,name=startTime,proto3" json:"startTime"`
	EndTime              string   `protobuf:"bytes,7,opt,name=endTime,proto3" json:"endTime"`
	Status               int64    `protobuf:"varint,8,opt,name=status,proto3" json:"status"`
	DeviceLimit          int64    `protobuf:"varint,9,opt,name=device_limit,json=deviceLimit,proto3" json:"device_limit"`
	ImageUrl             string   `protobuf:"bytes,10,opt,name=imageUrl,proto3" json:"imageUrl"`
	JumpPathType         int64    `protobuf:"varint,11,opt,name=jumpPathType,proto3" json:"jumpPathType"`
	JumpTime             int64    `protobuf:"varint,12,opt,name=jumpTime,proto3" json:"jumpTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetListResp_List) Reset()         { *m = GetListResp_List{} }
func (m *GetListResp_List) String() string { return proto.CompactTextString(m) }
func (*GetListResp_List) ProtoMessage()    {}
func (*GetListResp_List) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{7, 0}
}
func (m *GetListResp_List) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetListResp_List) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetListResp_List.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetListResp_List) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetListResp_List.Merge(dst, src)
}
func (m *GetListResp_List) XXX_Size() int {
	return m.Size()
}
func (m *GetListResp_List) XXX_DiscardUnknown() {
	xxx_messageInfo_GetListResp_List.DiscardUnknown(m)
}

var xxx_messageInfo_GetListResp_List proto.InternalMessageInfo

func (m *GetListResp_List) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetListResp_List) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetListResp_List) GetJumpPath() string {
	if m != nil {
		return m.JumpPath
	}
	return ""
}

func (m *GetListResp_List) GetDevicePlatform() string {
	if m != nil {
		return m.DevicePlatform
	}
	return ""
}

func (m *GetListResp_List) GetDeviceBuild() int64 {
	if m != nil {
		return m.DeviceBuild
	}
	return 0
}

func (m *GetListResp_List) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *GetListResp_List) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *GetListResp_List) GetStatus() int64 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetListResp_List) GetDeviceLimit() int64 {
	if m != nil {
		return m.DeviceLimit
	}
	return 0
}

func (m *GetListResp_List) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *GetListResp_List) GetJumpPathType() int64 {
	if m != nil {
		return m.JumpPathType
	}
	return 0
}

func (m *GetListResp_List) GetJumpTime() int64 {
	if m != nil {
		return m.JumpTime
	}
	return 0
}

// 获取当前有效闪屏配置(客户端)
type GetInfoReq struct {
	Platform             string   `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty" form:"platform" validate:"required"`
	Build                int64    `protobuf:"varint,2,opt,name=build,proto3" json:"build,omitempty" form:"build" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetInfoReq) Reset()         { *m = GetInfoReq{} }
func (m *GetInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetInfoReq) ProtoMessage()    {}
func (*GetInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{8}
}
func (m *GetInfoReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetInfoReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInfoReq.Merge(dst, src)
}
func (m *GetInfoReq) XXX_Size() int {
	return m.Size()
}
func (m *GetInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetInfoReq proto.InternalMessageInfo

func (m *GetInfoReq) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

func (m *GetInfoReq) GetBuild() int64 {
	if m != nil {
		return m.Build
	}
	return 0
}

type GetInfoResp struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	JumpPath             string   `protobuf:"bytes,3,opt,name=jumpPath,proto3" json:"jumpPath"`
	JumpTime             int64    `protobuf:"varint,4,opt,name=jumpTime,proto3" json:"jumpTime"`
	JumpPathType         int64    `protobuf:"varint,5,opt,name=jumpPathType,proto3" json:"jumpPathType"`
	ImageUrl             string   `protobuf:"bytes,6,opt,name=imageUrl,proto3" json:"imageUrl"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetInfoResp) Reset()         { *m = GetInfoResp{} }
func (m *GetInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetInfoResp) ProtoMessage()    {}
func (*GetInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{9}
}
func (m *GetInfoResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetInfoResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInfoResp.Merge(dst, src)
}
func (m *GetInfoResp) XXX_Size() int {
	return m.Size()
}
func (m *GetInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetInfoResp proto.InternalMessageInfo

func (m *GetInfoResp) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetInfoResp) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetInfoResp) GetJumpPath() string {
	if m != nil {
		return m.JumpPath
	}
	return ""
}

func (m *GetInfoResp) GetJumpTime() int64 {
	if m != nil {
		return m.JumpTime
	}
	return 0
}

func (m *GetInfoResp) GetJumpPathType() int64 {
	if m != nil {
		return m.JumpPathType
	}
	return 0
}

func (m *GetInfoResp) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

// 获取当前有效banner配置(客户端)
type GetBlinkBannerReq struct {
	Platform             string   `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty" form:"platform" validate:"required"`
	Build                int64    `protobuf:"varint,2,opt,name=build,proto3" json:"build,omitempty" form:"build" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBlinkBannerReq) Reset()         { *m = GetBlinkBannerReq{} }
func (m *GetBlinkBannerReq) String() string { return proto.CompactTextString(m) }
func (*GetBlinkBannerReq) ProtoMessage()    {}
func (*GetBlinkBannerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{10}
}
func (m *GetBlinkBannerReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetBlinkBannerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetBlinkBannerReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetBlinkBannerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBlinkBannerReq.Merge(dst, src)
}
func (m *GetBlinkBannerReq) XXX_Size() int {
	return m.Size()
}
func (m *GetBlinkBannerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBlinkBannerReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBlinkBannerReq proto.InternalMessageInfo

func (m *GetBlinkBannerReq) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

func (m *GetBlinkBannerReq) GetBuild() int64 {
	if m != nil {
		return m.Build
	}
	return 0
}

type GetBlinkBannerResp struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	JumpPath             string   `protobuf:"bytes,3,opt,name=jumpPath,proto3" json:"jumpPath"`
	JumpTime             int64    `protobuf:"varint,4,opt,name=jumpTime,proto3" json:"jumpTime"`
	JumpPathType         int64    `protobuf:"varint,5,opt,name=jumpPathType,proto3" json:"jumpPathType"`
	ImageUrl             string   `protobuf:"bytes,6,opt,name=imageUrl,proto3" json:"imageUrl"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBlinkBannerResp) Reset()         { *m = GetBlinkBannerResp{} }
func (m *GetBlinkBannerResp) String() string { return proto.CompactTextString(m) }
func (*GetBlinkBannerResp) ProtoMessage()    {}
func (*GetBlinkBannerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{11}
}
func (m *GetBlinkBannerResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetBlinkBannerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetBlinkBannerResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetBlinkBannerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBlinkBannerResp.Merge(dst, src)
}
func (m *GetBlinkBannerResp) XXX_Size() int {
	return m.Size()
}
func (m *GetBlinkBannerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBlinkBannerResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBlinkBannerResp proto.InternalMessageInfo

func (m *GetBlinkBannerResp) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetBlinkBannerResp) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetBlinkBannerResp) GetJumpPath() string {
	if m != nil {
		return m.JumpPath
	}
	return ""
}

func (m *GetBlinkBannerResp) GetJumpTime() int64 {
	if m != nil {
		return m.JumpTime
	}
	return 0
}

func (m *GetBlinkBannerResp) GetJumpPathType() int64 {
	if m != nil {
		return m.JumpPathType
	}
	return 0
}

func (m *GetBlinkBannerResp) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

// 获取banner配置(客户端)
type GetBannerReq struct {
	Platform             string   `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty" form:"platform" validate:"required"`
	Build                int64    `protobuf:"varint,2,opt,name=build,proto3" json:"build,omitempty" form:"build" validate:"required"`
	Type                 string   `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty" form:"type" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBannerReq) Reset()         { *m = GetBannerReq{} }
func (m *GetBannerReq) String() string { return proto.CompactTextString(m) }
func (*GetBannerReq) ProtoMessage()    {}
func (*GetBannerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{12}
}
func (m *GetBannerReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetBannerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetBannerReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetBannerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannerReq.Merge(dst, src)
}
func (m *GetBannerReq) XXX_Size() int {
	return m.Size()
}
func (m *GetBannerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannerReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannerReq proto.InternalMessageInfo

func (m *GetBannerReq) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

func (m *GetBannerReq) GetBuild() int64 {
	if m != nil {
		return m.Build
	}
	return 0
}

func (m *GetBannerReq) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

type GetBannerResp struct {
	List                 []*GetBannerResp_List `protobuf:"bytes,1,rep,name=list" json:"list"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetBannerResp) Reset()         { *m = GetBannerResp{} }
func (m *GetBannerResp) String() string { return proto.CompactTextString(m) }
func (*GetBannerResp) ProtoMessage()    {}
func (*GetBannerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{13}
}
func (m *GetBannerResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetBannerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetBannerResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetBannerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannerResp.Merge(dst, src)
}
func (m *GetBannerResp) XXX_Size() int {
	return m.Size()
}
func (m *GetBannerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannerResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannerResp proto.InternalMessageInfo

func (m *GetBannerResp) GetList() []*GetBannerResp_List {
	if m != nil {
		return m.List
	}
	return nil
}

type GetBannerResp_List struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	JumpPath             string   `protobuf:"bytes,3,opt,name=jumpPath,proto3" json:"jumpPath"`
	JumpTime             int64    `protobuf:"varint,4,opt,name=jumpTime,proto3" json:"jumpTime"`
	JumpPathType         int64    `protobuf:"varint,5,opt,name=jumpPathType,proto3" json:"jumpPathType"`
	ImageUrl             string   `protobuf:"bytes,6,opt,name=imageUrl,proto3" json:"imageUrl"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBannerResp_List) Reset()         { *m = GetBannerResp_List{} }
func (m *GetBannerResp_List) String() string { return proto.CompactTextString(m) }
func (*GetBannerResp_List) ProtoMessage()    {}
func (*GetBannerResp_List) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{13, 0}
}
func (m *GetBannerResp_List) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetBannerResp_List) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetBannerResp_List.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetBannerResp_List) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannerResp_List.Merge(dst, src)
}
func (m *GetBannerResp_List) XXX_Size() int {
	return m.Size()
}
func (m *GetBannerResp_List) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannerResp_List.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannerResp_List proto.InternalMessageInfo

func (m *GetBannerResp_List) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetBannerResp_List) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetBannerResp_List) GetJumpPath() string {
	if m != nil {
		return m.JumpPath
	}
	return ""
}

func (m *GetBannerResp_List) GetJumpTime() int64 {
	if m != nil {
		return m.JumpTime
	}
	return 0
}

func (m *GetBannerResp_List) GetJumpPathType() int64 {
	if m != nil {
		return m.JumpPathType
	}
	return 0
}

func (m *GetBannerResp_List) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

type GetPlatformListReq struct {
	Type                 int64    `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlatformListReq) Reset()         { *m = GetPlatformListReq{} }
func (m *GetPlatformListReq) String() string { return proto.CompactTextString(m) }
func (*GetPlatformListReq) ProtoMessage()    {}
func (*GetPlatformListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{14}
}
func (m *GetPlatformListReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetPlatformListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetPlatformListReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetPlatformListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlatformListReq.Merge(dst, src)
}
func (m *GetPlatformListReq) XXX_Size() int {
	return m.Size()
}
func (m *GetPlatformListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlatformListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlatformListReq proto.InternalMessageInfo

func (m *GetPlatformListReq) GetType() int64 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GetPlatformListResp struct {
	Platform             []string `protobuf:"bytes,1,rep,name=platform" json:"platform"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlatformListResp) Reset()         { *m = GetPlatformListResp{} }
func (m *GetPlatformListResp) String() string { return proto.CompactTextString(m) }
func (*GetPlatformListResp) ProtoMessage()    {}
func (*GetPlatformListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{15}
}
func (m *GetPlatformListResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetPlatformListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetPlatformListResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetPlatformListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlatformListResp.Merge(dst, src)
}
func (m *GetPlatformListResp) XXX_Size() int {
	return m.Size()
}
func (m *GetPlatformListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlatformListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlatformListResp proto.InternalMessageInfo

func (m *GetPlatformListResp) GetPlatform() []string {
	if m != nil {
		return m.Platform
	}
	return nil
}

type GetListExReq struct {
	Platform             string   `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty" form:"platform" validate:"required"`
	Page                 int64    `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty" form:"page"`
	PageSize             int64    `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty" form:"pageSize"`
	Type                 []string `protobuf:"bytes,4,rep,name=type" json:"type,omitempty" form:"type" validate:"required"`
	DevicePlatform       string   `protobuf:"bytes,5,opt,name=device_platform,json=devicePlatform,proto3" json:"device_platform,omitempty" form:"device_platform"`
	Status               string   `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty" form:"status"`
	StartTime            string   `protobuf:"bytes,7,opt,name=startTime,proto3" json:"startTime,omitempty" form:"startTime"`
	EndTime              string   `protobuf:"bytes,8,opt,name=endTime,proto3" json:"endTime,omitempty" form:"endTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetListExReq) Reset()         { *m = GetListExReq{} }
func (m *GetListExReq) String() string { return proto.CompactTextString(m) }
func (*GetListExReq) ProtoMessage()    {}
func (*GetListExReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{16}
}
func (m *GetListExReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetListExReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetListExReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetListExReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetListExReq.Merge(dst, src)
}
func (m *GetListExReq) XXX_Size() int {
	return m.Size()
}
func (m *GetListExReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetListExReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetListExReq proto.InternalMessageInfo

func (m *GetListExReq) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

func (m *GetListExReq) GetPage() int64 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetListExReq) GetPageSize() int64 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetListExReq) GetType() []string {
	if m != nil {
		return m.Type
	}
	return nil
}

func (m *GetListExReq) GetDevicePlatform() string {
	if m != nil {
		return m.DevicePlatform
	}
	return ""
}

func (m *GetListExReq) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *GetListExReq) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *GetListExReq) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

type GetListExResp struct {
	CurrentPage          int64                 `protobuf:"varint,1,opt,name=currentPage,proto3" json:"currentPage"`
	TotalCount           int64                 `protobuf:"varint,2,opt,name=totalCount,proto3" json:"totalCount"`
	List                 []*GetListExResp_List `protobuf:"bytes,3,rep,name=list" json:"list"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetListExResp) Reset()         { *m = GetListExResp{} }
func (m *GetListExResp) String() string { return proto.CompactTextString(m) }
func (*GetListExResp) ProtoMessage()    {}
func (*GetListExResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{17}
}
func (m *GetListExResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetListExResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetListExResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetListExResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetListExResp.Merge(dst, src)
}
func (m *GetListExResp) XXX_Size() int {
	return m.Size()
}
func (m *GetListExResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetListExResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetListExResp proto.InternalMessageInfo

func (m *GetListExResp) GetCurrentPage() int64 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *GetListExResp) GetTotalCount() int64 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *GetListExResp) GetList() []*GetListExResp_List {
	if m != nil {
		return m.List
	}
	return nil
}

type GetListExResp_List struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	JumpPath             string   `protobuf:"bytes,3,opt,name=jumpPath,proto3" json:"jumpPath"`
	DevicePlatform       string   `protobuf:"bytes,4,opt,name=device_platform,json=devicePlatform,proto3" json:"device_platform"`
	DeviceBuild          int64    `protobuf:"varint,5,opt,name=device_build,json=deviceBuild,proto3" json:"device_build"`
	StartTime            string   `protobuf:"bytes,6,opt,name=startTime,proto3" json:"startTime"`
	EndTime              string   `protobuf:"bytes,7,opt,name=endTime,proto3" json:"endTime"`
	Status               int64    `protobuf:"varint,8,opt,name=status,proto3" json:"status"`
	DeviceLimit          int64    `protobuf:"varint,9,opt,name=device_limit,json=deviceLimit,proto3" json:"device_limit"`
	ImageUrl             string   `protobuf:"bytes,10,opt,name=imageUrl,proto3" json:"imageUrl"`
	JumpPathType         int64    `protobuf:"varint,11,opt,name=jumpPathType,proto3" json:"jumpPathType"`
	JumpTime             int64    `protobuf:"varint,12,opt,name=jumpTime,proto3" json:"jumpTime"`
	Type                 string   `protobuf:"bytes,13,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetListExResp_List) Reset()         { *m = GetListExResp_List{} }
func (m *GetListExResp_List) String() string { return proto.CompactTextString(m) }
func (*GetListExResp_List) ProtoMessage()    {}
func (*GetListExResp_List) Descriptor() ([]byte, []int) {
	return fileDescriptor_resource_c1968bdee8bb6892, []int{17, 0}
}
func (m *GetListExResp_List) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetListExResp_List) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetListExResp_List.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetListExResp_List) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetListExResp_List.Merge(dst, src)
}
func (m *GetListExResp_List) XXX_Size() int {
	return m.Size()
}
func (m *GetListExResp_List) XXX_DiscardUnknown() {
	xxx_messageInfo_GetListExResp_List.DiscardUnknown(m)
}

var xxx_messageInfo_GetListExResp_List proto.InternalMessageInfo

func (m *GetListExResp_List) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetListExResp_List) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetListExResp_List) GetJumpPath() string {
	if m != nil {
		return m.JumpPath
	}
	return ""
}

func (m *GetListExResp_List) GetDevicePlatform() string {
	if m != nil {
		return m.DevicePlatform
	}
	return ""
}

func (m *GetListExResp_List) GetDeviceBuild() int64 {
	if m != nil {
		return m.DeviceBuild
	}
	return 0
}

func (m *GetListExResp_List) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *GetListExResp_List) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *GetListExResp_List) GetStatus() int64 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetListExResp_List) GetDeviceLimit() int64 {
	if m != nil {
		return m.DeviceLimit
	}
	return 0
}

func (m *GetListExResp_List) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *GetListExResp_List) GetJumpPathType() int64 {
	if m != nil {
		return m.JumpPathType
	}
	return 0
}

func (m *GetListExResp_List) GetJumpTime() int64 {
	if m != nil {
		return m.JumpTime
	}
	return 0
}

func (m *GetListExResp_List) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func init() {
	proto.RegisterType((*AddReq)(nil), "live.liveadmin.v1.AddReq")
	proto.RegisterType((*AddResp)(nil), "live.liveadmin.v1.AddResp")
	proto.RegisterType((*EditReq)(nil), "live.liveadmin.v1.EditReq")
	proto.RegisterType((*EditResp)(nil), "live.liveadmin.v1.EditResp")
	proto.RegisterType((*OfflineReq)(nil), "live.liveadmin.v1.OfflineReq")
	proto.RegisterType((*OfflineResp)(nil), "live.liveadmin.v1.OfflineResp")
	proto.RegisterType((*GetListReq)(nil), "live.liveadmin.v1.GetListReq")
	proto.RegisterType((*GetListResp)(nil), "live.liveadmin.v1.GetListResp")
	proto.RegisterType((*GetListResp_List)(nil), "live.liveadmin.v1.GetListResp.List")
	proto.RegisterType((*GetInfoReq)(nil), "live.liveadmin.v1.GetInfoReq")
	proto.RegisterType((*GetInfoResp)(nil), "live.liveadmin.v1.GetInfoResp")
	proto.RegisterType((*GetBlinkBannerReq)(nil), "live.liveadmin.v1.GetBlinkBannerReq")
	proto.RegisterType((*GetBlinkBannerResp)(nil), "live.liveadmin.v1.GetBlinkBannerResp")
	proto.RegisterType((*GetBannerReq)(nil), "live.liveadmin.v1.GetBannerReq")
	proto.RegisterType((*GetBannerResp)(nil), "live.liveadmin.v1.GetBannerResp")
	proto.RegisterType((*GetBannerResp_List)(nil), "live.liveadmin.v1.GetBannerResp.List")
	proto.RegisterType((*GetPlatformListReq)(nil), "live.liveadmin.v1.GetPlatformListReq")
	proto.RegisterType((*GetPlatformListResp)(nil), "live.liveadmin.v1.GetPlatformListResp")
	proto.RegisterType((*GetListExReq)(nil), "live.liveadmin.v1.GetListExReq")
	proto.RegisterType((*GetListExResp)(nil), "live.liveadmin.v1.GetListExResp")
	proto.RegisterType((*GetListExResp_List)(nil), "live.liveadmin.v1.GetListExResp.List")
}
func (m *AddReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Platform) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Platform)))
		i += copy(dAtA[i:], m.Platform)
	}
	if len(m.Title) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Title)))
		i += copy(dAtA[i:], m.Title)
	}
	if len(m.JumpPath) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.JumpPath)))
		i += copy(dAtA[i:], m.JumpPath)
	}
	if m.JumpTime != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.JumpTime))
	}
	if len(m.Type) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Type)))
		i += copy(dAtA[i:], m.Type)
	}
	if len(m.Device) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Device)))
		i += copy(dAtA[i:], m.Device)
	}
	if len(m.StartTime) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.StartTime)))
		i += copy(dAtA[i:], m.StartTime)
	}
	if len(m.EndTime) > 0 {
		dAtA[i] = 0x42
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.EndTime)))
		i += copy(dAtA[i:], m.EndTime)
	}
	if len(m.ImageUrl) > 0 {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.ImageUrl)))
		i += copy(dAtA[i:], m.ImageUrl)
	}
	if m.JumpPathType != 0 {
		dAtA[i] = 0x50
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.JumpPathType))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *AddResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Id) > 0 {
		dAtA2 := make([]byte, len(m.Id)*10)
		var j1 int
		for _, num1 := range m.Id {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA2[j1] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j1++
			}
			dAtA2[j1] = uint8(num)
			j1++
		}
		dAtA[i] = 0xa
		i++
		i = encodeVarintResource(dAtA, i, uint64(j1))
		i += copy(dAtA[i:], dAtA2[:j1])
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *EditReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EditReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Platform) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Platform)))
		i += copy(dAtA[i:], m.Platform)
	}
	if m.Id != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.Id))
	}
	if len(m.Title) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Title)))
		i += copy(dAtA[i:], m.Title)
	}
	if len(m.JumpPath) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.JumpPath)))
		i += copy(dAtA[i:], m.JumpPath)
	}
	if m.JumpTime != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.JumpTime))
	}
	if len(m.StartTime) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.StartTime)))
		i += copy(dAtA[i:], m.StartTime)
	}
	if len(m.EndTime) > 0 {
		dAtA[i] = 0x42
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.EndTime)))
		i += copy(dAtA[i:], m.EndTime)
	}
	if len(m.ImageUrl) > 0 {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.ImageUrl)))
		i += copy(dAtA[i:], m.ImageUrl)
	}
	if m.JumpPathType != 0 {
		dAtA[i] = 0x50
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.JumpPathType))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *EditResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EditResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *OfflineReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OfflineReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Platform) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Platform)))
		i += copy(dAtA[i:], m.Platform)
	}
	if m.Id != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.Id))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *OfflineResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OfflineResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Platform) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Platform)))
		i += copy(dAtA[i:], m.Platform)
	}
	if m.Page != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.Page))
	}
	if m.PageSize != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.PageSize))
	}
	if len(m.Type) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Type)))
		i += copy(dAtA[i:], m.Type)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CurrentPage != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.CurrentPage))
	}
	if m.TotalCount != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.TotalCount))
	}
	if len(m.List) > 0 {
		for _, msg := range m.List {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintResource(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetListResp_List) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetListResp_List) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Id != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.Id))
	}
	if len(m.Title) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Title)))
		i += copy(dAtA[i:], m.Title)
	}
	if len(m.JumpPath) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.JumpPath)))
		i += copy(dAtA[i:], m.JumpPath)
	}
	if len(m.DevicePlatform) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.DevicePlatform)))
		i += copy(dAtA[i:], m.DevicePlatform)
	}
	if m.DeviceBuild != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.DeviceBuild))
	}
	if len(m.StartTime) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.StartTime)))
		i += copy(dAtA[i:], m.StartTime)
	}
	if len(m.EndTime) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.EndTime)))
		i += copy(dAtA[i:], m.EndTime)
	}
	if m.Status != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.Status))
	}
	if m.DeviceLimit != 0 {
		dAtA[i] = 0x48
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.DeviceLimit))
	}
	if len(m.ImageUrl) > 0 {
		dAtA[i] = 0x52
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.ImageUrl)))
		i += copy(dAtA[i:], m.ImageUrl)
	}
	if m.JumpPathType != 0 {
		dAtA[i] = 0x58
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.JumpPathType))
	}
	if m.JumpTime != 0 {
		dAtA[i] = 0x60
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.JumpTime))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Platform) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Platform)))
		i += copy(dAtA[i:], m.Platform)
	}
	if m.Build != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.Build))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Id != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.Id))
	}
	if len(m.Title) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Title)))
		i += copy(dAtA[i:], m.Title)
	}
	if len(m.JumpPath) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.JumpPath)))
		i += copy(dAtA[i:], m.JumpPath)
	}
	if m.JumpTime != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.JumpTime))
	}
	if m.JumpPathType != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.JumpPathType))
	}
	if len(m.ImageUrl) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.ImageUrl)))
		i += copy(dAtA[i:], m.ImageUrl)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetBlinkBannerReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBlinkBannerReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Platform) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Platform)))
		i += copy(dAtA[i:], m.Platform)
	}
	if m.Build != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.Build))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetBlinkBannerResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBlinkBannerResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Id != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.Id))
	}
	if len(m.Title) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Title)))
		i += copy(dAtA[i:], m.Title)
	}
	if len(m.JumpPath) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.JumpPath)))
		i += copy(dAtA[i:], m.JumpPath)
	}
	if m.JumpTime != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.JumpTime))
	}
	if m.JumpPathType != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.JumpPathType))
	}
	if len(m.ImageUrl) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.ImageUrl)))
		i += copy(dAtA[i:], m.ImageUrl)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetBannerReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBannerReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Platform) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Platform)))
		i += copy(dAtA[i:], m.Platform)
	}
	if m.Build != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.Build))
	}
	if len(m.Type) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Type)))
		i += copy(dAtA[i:], m.Type)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetBannerResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBannerResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.List) > 0 {
		for _, msg := range m.List {
			dAtA[i] = 0xa
			i++
			i = encodeVarintResource(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetBannerResp_List) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBannerResp_List) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Id != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.Id))
	}
	if len(m.Title) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Title)))
		i += copy(dAtA[i:], m.Title)
	}
	if len(m.JumpPath) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.JumpPath)))
		i += copy(dAtA[i:], m.JumpPath)
	}
	if m.JumpTime != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.JumpTime))
	}
	if m.JumpPathType != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.JumpPathType))
	}
	if len(m.ImageUrl) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.ImageUrl)))
		i += copy(dAtA[i:], m.ImageUrl)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetPlatformListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPlatformListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Type != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.Type))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetPlatformListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPlatformListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Platform) > 0 {
		for _, s := range m.Platform {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetListExReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetListExReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Platform) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Platform)))
		i += copy(dAtA[i:], m.Platform)
	}
	if m.Page != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.Page))
	}
	if m.PageSize != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.PageSize))
	}
	if len(m.Type) > 0 {
		for _, s := range m.Type {
			dAtA[i] = 0x22
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.DevicePlatform) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.DevicePlatform)))
		i += copy(dAtA[i:], m.DevicePlatform)
	}
	if len(m.Status) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Status)))
		i += copy(dAtA[i:], m.Status)
	}
	if len(m.StartTime) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.StartTime)))
		i += copy(dAtA[i:], m.StartTime)
	}
	if len(m.EndTime) > 0 {
		dAtA[i] = 0x42
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.EndTime)))
		i += copy(dAtA[i:], m.EndTime)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetListExResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetListExResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CurrentPage != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.CurrentPage))
	}
	if m.TotalCount != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.TotalCount))
	}
	if len(m.List) > 0 {
		for _, msg := range m.List {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintResource(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetListExResp_List) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetListExResp_List) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Id != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.Id))
	}
	if len(m.Title) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Title)))
		i += copy(dAtA[i:], m.Title)
	}
	if len(m.JumpPath) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.JumpPath)))
		i += copy(dAtA[i:], m.JumpPath)
	}
	if len(m.DevicePlatform) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.DevicePlatform)))
		i += copy(dAtA[i:], m.DevicePlatform)
	}
	if m.DeviceBuild != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.DeviceBuild))
	}
	if len(m.StartTime) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.StartTime)))
		i += copy(dAtA[i:], m.StartTime)
	}
	if len(m.EndTime) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.EndTime)))
		i += copy(dAtA[i:], m.EndTime)
	}
	if m.Status != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.Status))
	}
	if m.DeviceLimit != 0 {
		dAtA[i] = 0x48
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.DeviceLimit))
	}
	if len(m.ImageUrl) > 0 {
		dAtA[i] = 0x52
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.ImageUrl)))
		i += copy(dAtA[i:], m.ImageUrl)
	}
	if m.JumpPathType != 0 {
		dAtA[i] = 0x58
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.JumpPathType))
	}
	if m.JumpTime != 0 {
		dAtA[i] = 0x60
		i++
		i = encodeVarintResource(dAtA, i, uint64(m.JumpTime))
	}
	if len(m.Type) > 0 {
		dAtA[i] = 0x6a
		i++
		i = encodeVarintResource(dAtA, i, uint64(len(m.Type)))
		i += copy(dAtA[i:], m.Type)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func encodeVarintResource(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *AddReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Platform)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.Title)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.JumpPath)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.JumpTime != 0 {
		n += 1 + sovResource(uint64(m.JumpTime))
	}
	l = len(m.Type)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.Device)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.StartTime)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.EndTime)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.ImageUrl)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.JumpPathType != 0 {
		n += 1 + sovResource(uint64(m.JumpPathType))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *AddResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Id) > 0 {
		l = 0
		for _, e := range m.Id {
			l += sovResource(uint64(e))
		}
		n += 1 + sovResource(uint64(l)) + l
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *EditReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Platform)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.Id != 0 {
		n += 1 + sovResource(uint64(m.Id))
	}
	l = len(m.Title)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.JumpPath)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.JumpTime != 0 {
		n += 1 + sovResource(uint64(m.JumpTime))
	}
	l = len(m.StartTime)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.EndTime)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.ImageUrl)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.JumpPathType != 0 {
		n += 1 + sovResource(uint64(m.JumpPathType))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *EditResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *OfflineReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Platform)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.Id != 0 {
		n += 1 + sovResource(uint64(m.Id))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *OfflineResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetListReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Platform)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.Page != 0 {
		n += 1 + sovResource(uint64(m.Page))
	}
	if m.PageSize != 0 {
		n += 1 + sovResource(uint64(m.PageSize))
	}
	l = len(m.Type)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetListResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CurrentPage != 0 {
		n += 1 + sovResource(uint64(m.CurrentPage))
	}
	if m.TotalCount != 0 {
		n += 1 + sovResource(uint64(m.TotalCount))
	}
	if len(m.List) > 0 {
		for _, e := range m.List {
			l = e.Size()
			n += 1 + l + sovResource(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetListResp_List) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != 0 {
		n += 1 + sovResource(uint64(m.Id))
	}
	l = len(m.Title)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.JumpPath)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.DevicePlatform)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.DeviceBuild != 0 {
		n += 1 + sovResource(uint64(m.DeviceBuild))
	}
	l = len(m.StartTime)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.EndTime)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.Status != 0 {
		n += 1 + sovResource(uint64(m.Status))
	}
	if m.DeviceLimit != 0 {
		n += 1 + sovResource(uint64(m.DeviceLimit))
	}
	l = len(m.ImageUrl)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.JumpPathType != 0 {
		n += 1 + sovResource(uint64(m.JumpPathType))
	}
	if m.JumpTime != 0 {
		n += 1 + sovResource(uint64(m.JumpTime))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetInfoReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Platform)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.Build != 0 {
		n += 1 + sovResource(uint64(m.Build))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetInfoResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != 0 {
		n += 1 + sovResource(uint64(m.Id))
	}
	l = len(m.Title)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.JumpPath)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.JumpTime != 0 {
		n += 1 + sovResource(uint64(m.JumpTime))
	}
	if m.JumpPathType != 0 {
		n += 1 + sovResource(uint64(m.JumpPathType))
	}
	l = len(m.ImageUrl)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetBlinkBannerReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Platform)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.Build != 0 {
		n += 1 + sovResource(uint64(m.Build))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetBlinkBannerResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != 0 {
		n += 1 + sovResource(uint64(m.Id))
	}
	l = len(m.Title)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.JumpPath)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.JumpTime != 0 {
		n += 1 + sovResource(uint64(m.JumpTime))
	}
	if m.JumpPathType != 0 {
		n += 1 + sovResource(uint64(m.JumpPathType))
	}
	l = len(m.ImageUrl)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetBannerReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Platform)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.Build != 0 {
		n += 1 + sovResource(uint64(m.Build))
	}
	l = len(m.Type)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetBannerResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.List) > 0 {
		for _, e := range m.List {
			l = e.Size()
			n += 1 + l + sovResource(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetBannerResp_List) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != 0 {
		n += 1 + sovResource(uint64(m.Id))
	}
	l = len(m.Title)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.JumpPath)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.JumpTime != 0 {
		n += 1 + sovResource(uint64(m.JumpTime))
	}
	if m.JumpPathType != 0 {
		n += 1 + sovResource(uint64(m.JumpPathType))
	}
	l = len(m.ImageUrl)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetPlatformListReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovResource(uint64(m.Type))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetPlatformListResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Platform) > 0 {
		for _, s := range m.Platform {
			l = len(s)
			n += 1 + l + sovResource(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetListExReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Platform)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.Page != 0 {
		n += 1 + sovResource(uint64(m.Page))
	}
	if m.PageSize != 0 {
		n += 1 + sovResource(uint64(m.PageSize))
	}
	if len(m.Type) > 0 {
		for _, s := range m.Type {
			l = len(s)
			n += 1 + l + sovResource(uint64(l))
		}
	}
	l = len(m.DevicePlatform)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.Status)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.StartTime)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.EndTime)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetListExResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CurrentPage != 0 {
		n += 1 + sovResource(uint64(m.CurrentPage))
	}
	if m.TotalCount != 0 {
		n += 1 + sovResource(uint64(m.TotalCount))
	}
	if len(m.List) > 0 {
		for _, e := range m.List {
			l = e.Size()
			n += 1 + l + sovResource(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetListExResp_List) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != 0 {
		n += 1 + sovResource(uint64(m.Id))
	}
	l = len(m.Title)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.JumpPath)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.DevicePlatform)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.DeviceBuild != 0 {
		n += 1 + sovResource(uint64(m.DeviceBuild))
	}
	l = len(m.StartTime)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	l = len(m.EndTime)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.Status != 0 {
		n += 1 + sovResource(uint64(m.Status))
	}
	if m.DeviceLimit != 0 {
		n += 1 + sovResource(uint64(m.DeviceLimit))
	}
	l = len(m.ImageUrl)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.JumpPathType != 0 {
		n += 1 + sovResource(uint64(m.JumpPathType))
	}
	if m.JumpTime != 0 {
		n += 1 + sovResource(uint64(m.JumpTime))
	}
	l = len(m.Type)
	if l > 0 {
		n += 1 + l + sovResource(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovResource(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozResource(x uint64) (n int) {
	return sovResource(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *AddReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AddReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AddReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Platform = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpPath", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.JumpPath = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpTime", wireType)
			}
			m.JumpTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JumpTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Device", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Device = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImageUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImageUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpPathType", wireType)
			}
			m.JumpPathType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JumpPathType |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AddResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AddResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v int64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowResource
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (int64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Id = append(m.Id, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowResource
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthResource
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.Id) == 0 {
					m.Id = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowResource
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (int64(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Id = append(m.Id, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EditReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EditReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EditReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Platform = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpPath", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.JumpPath = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpTime", wireType)
			}
			m.JumpTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JumpTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImageUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImageUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpPathType", wireType)
			}
			m.JumpPathType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JumpPathType |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EditResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EditResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EditResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OfflineReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: OfflineReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: OfflineReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Platform = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OfflineResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: OfflineResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: OfflineResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Platform = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Page", wireType)
			}
			m.Page = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Page |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PageSize", wireType)
			}
			m.PageSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PageSize |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CurrentPage", wireType)
			}
			m.CurrentPage = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrentPage |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TotalCount", wireType)
			}
			m.TotalCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalCount |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field List", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.List = append(m.List, &GetListResp_List{})
			if err := m.List[len(m.List)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetListResp_List) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: List: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: List: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpPath", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.JumpPath = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DevicePlatform", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DevicePlatform = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceBuild", wireType)
			}
			m.DeviceBuild = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeviceBuild |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceLimit", wireType)
			}
			m.DeviceLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeviceLimit |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImageUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImageUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpPathType", wireType)
			}
			m.JumpPathType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JumpPathType |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpTime", wireType)
			}
			m.JumpTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JumpTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Platform = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Build", wireType)
			}
			m.Build = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Build |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpPath", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.JumpPath = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpTime", wireType)
			}
			m.JumpTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JumpTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpPathType", wireType)
			}
			m.JumpPathType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JumpPathType |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImageUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImageUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBlinkBannerReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetBlinkBannerReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetBlinkBannerReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Platform = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Build", wireType)
			}
			m.Build = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Build |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBlinkBannerResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetBlinkBannerResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetBlinkBannerResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpPath", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.JumpPath = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpTime", wireType)
			}
			m.JumpTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JumpTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpPathType", wireType)
			}
			m.JumpPathType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JumpPathType |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImageUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImageUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBannerReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetBannerReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetBannerReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Platform = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Build", wireType)
			}
			m.Build = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Build |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBannerResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetBannerResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetBannerResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field List", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.List = append(m.List, &GetBannerResp_List{})
			if err := m.List[len(m.List)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBannerResp_List) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: List: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: List: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpPath", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.JumpPath = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpTime", wireType)
			}
			m.JumpTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JumpTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpPathType", wireType)
			}
			m.JumpPathType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JumpPathType |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImageUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImageUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPlatformListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetPlatformListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetPlatformListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPlatformListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetPlatformListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetPlatformListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Platform = append(m.Platform, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetListExReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetListExReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetListExReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Platform = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Page", wireType)
			}
			m.Page = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Page |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PageSize", wireType)
			}
			m.PageSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PageSize |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = append(m.Type, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DevicePlatform", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DevicePlatform = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Status = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetListExResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetListExResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetListExResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CurrentPage", wireType)
			}
			m.CurrentPage = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrentPage |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TotalCount", wireType)
			}
			m.TotalCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalCount |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field List", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.List = append(m.List, &GetListExResp_List{})
			if err := m.List[len(m.List)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetListExResp_List) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: List: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: List: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpPath", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.JumpPath = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DevicePlatform", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DevicePlatform = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceBuild", wireType)
			}
			m.DeviceBuild = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeviceBuild |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceLimit", wireType)
			}
			m.DeviceLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeviceLimit |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImageUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImageUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpPathType", wireType)
			}
			m.JumpPathType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JumpPathType |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field JumpTime", wireType)
			}
			m.JumpTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JumpTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResource
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthResource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipResource(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowResource
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowResource
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowResource
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthResource
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowResource
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipResource(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthResource = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowResource   = fmt.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("resource.proto", fileDescriptor_resource_c1968bdee8bb6892) }

var fileDescriptor_resource_c1968bdee8bb6892 = []byte{
	// 1302 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x58, 0xcf, 0x6f, 0x1b, 0xc5,
	0x17, 0xd7, 0x7a, 0x6d, 0xc7, 0x79, 0x76, 0x7e, 0x74, 0xfa, 0x55, 0xbf, 0xc6, 0xd0, 0x8c, 0xb3,
	0x69, 0x82, 0x11, 0xe0, 0x28, 0x2e, 0x12, 0x52, 0x28, 0x94, 0x3a, 0x0a, 0x05, 0xa9, 0xa2, 0xd1,
	0xb6, 0x5c, 0xb8, 0xc0, 0x26, 0xbb, 0x71, 0x06, 0xd6, 0xf6, 0xc6, 0x3b, 0x8e, 0x5a, 0x6e, 0x48,
	0x5c, 0x11, 0x07, 0xf8, 0x27, 0x38, 0x22, 0xf1, 0x47, 0x14, 0x4e, 0xdc, 0x91, 0x46, 0x55, 0x8e,
	0xe6, 0x80, 0xb4, 0xdc, 0x38, 0xa1, 0x9d, 0x1f, 0xeb, 0x5d, 0x7b, 0x37, 0x24, 0x90, 0x96, 0x20,
	0x7a, 0x49, 0x66, 0xde, 0x9b, 0xf7, 0xfc, 0xe6, 0xbd, 0xcf, 0xfb, 0x31, 0x0b, 0xf3, 0x03, 0xc7,
	0xef, 0x0f, 0x07, 0x7b, 0x4e, 0xd3, 0x1b, 0xf4, 0x69, 0x1f, 0x5d, 0x72, 0xc9, 0x91, 0xd3, 0x0c,
	0xff, 0x58, 0x76, 0x97, 0xf4, 0x9a, 0x47, 0x1b, 0xb5, 0x57, 0x3b, 0x84, 0x1e, 0x0c, 0x77, 0x9b,
	0x7b, 0xfd, 0xee, 0x7a, 0xa7, 0xdf, 0xe9, 0xaf, 0xf3, 0x93, 0xbb, 0xc3, 0x7d, 0xbe, 0xe3, 0x1b,
	0xbe, 0x12, 0x1a, 0x8c, 0x5f, 0xf3, 0x50, 0xbc, 0x65, 0xdb, 0xa6, 0x73, 0x88, 0xb6, 0xa0, 0xe4,
	0xb9, 0x16, 0xdd, 0xef, 0x0f, 0xba, 0x55, 0xad, 0xae, 0x35, 0x66, 0xdb, 0x2f, 0x06, 0x0c, 0xaf,
	0x84, 0xfb, 0x4d, 0x43, 0x71, 0x8c, 0xfa, 0x91, 0xe5, 0x12, 0xdb, 0xa2, 0xce, 0xa6, 0x31, 0x70,
	0x0e, 0x87, 0x64, 0xe0, 0xd8, 0x86, 0x19, 0x09, 0xa2, 0x4d, 0x28, 0x50, 0x42, 0x5d, 0xa7, 0x9a,
	0xe3, 0x1a, 0xae, 0x05, 0x0c, 0xd7, 0x85, 0x06, 0x4e, 0x4e, 0x17, 0x17, 0x22, 0x68, 0x1d, 0x4a,
	0x9f, 0x0c, 0xbb, 0xde, 0x8e, 0x45, 0x0f, 0xaa, 0x3a, 0x17, 0xbf, 0x1c, 0x30, 0xbc, 0x20, 0xc4,
	0x15, 0xc7, 0x30, 0xa3, 0x43, 0x4a, 0xe0, 0x3e, 0xe9, 0x3a, 0xd5, 0x7c, 0x5d, 0x6b, 0xe8, 0x93,
	0x02, 0x21, 0x47, 0x0a, 0x84, 0x4b, 0xf4, 0x3a, 0xe4, 0xe9, 0x43, 0xcf, 0xa9, 0x16, 0xb8, 0xf6,
	0x95, 0x80, 0x61, 0x2c, 0x8d, 0x7b, 0xe8, 0x65, 0xd8, 0xc6, 0x05, 0xd0, 0x9b, 0x50, 0xb4, 0x9d,
	0x23, 0xb2, 0xe7, 0x54, 0x8b, 0x5c, 0x74, 0x35, 0x60, 0x78, 0x59, 0x88, 0x0a, 0x7a, 0xba, 0xb0,
	0x14, 0x42, 0xef, 0xc0, 0xac, 0x4f, 0xad, 0x01, 0xe5, 0x96, 0xce, 0x70, 0x0d, 0x8d, 0x80, 0xe1,
	0x6b, 0x42, 0x43, 0xc4, 0x4a, 0x57, 0x32, 0x16, 0x45, 0x6f, 0xc3, 0x8c, 0xd3, 0xb3, 0xb9, 0x96,
	0x12, 0xd7, 0xb2, 0x16, 0x30, 0x6c, 0x08, 0x2d, 0x92, 0x91, 0xae, 0x43, 0x89, 0x85, 0x41, 0x26,
	0x5d, 0xab, 0xe3, 0x7c, 0x30, 0x70, 0xab, 0xb3, 0x93, 0x41, 0x56, 0x9c, 0x8c, 0x20, 0x2b, 0x36,
	0x7a, 0x03, 0x2a, 0x2a, 0x06, 0xf7, 0x43, 0x77, 0x02, 0xf7, 0xfd, 0xff, 0x03, 0x86, 0x2f, 0x27,
	0x83, 0x15, 0x72, 0x0d, 0x33, 0x71, 0xd8, 0x58, 0x86, 0x19, 0x0e, 0x38, 0xdf, 0x43, 0x57, 0x20,
	0x47, 0xec, 0xaa, 0x56, 0xd7, 0x1b, 0x7a, 0xbb, 0x38, 0x62, 0x38, 0x47, 0x6c, 0x33, 0x47, 0x6c,
	0xe3, 0x67, 0x1d, 0x66, 0xb6, 0x6d, 0x42, 0xcf, 0x0d, 0x95, 0x1b, 0xfc, 0x87, 0x72, 0xdc, 0xcc,
	0xe5, 0x80, 0xe1, 0xab, 0xf2, 0xbe, 0x76, 0xba, 0x60, 0x8e, 0xd8, 0x68, 0x4d, 0x01, 0x59, 0x20,
	0x71, 0x31, 0x60, 0xb8, 0x12, 0x07, 0x72, 0x1a, 0x68, 0xf3, 0x67, 0x05, 0x6d, 0xe1, 0x34, 0xa0,
	0x6d, 0x4d, 0x83, 0xe7, 0x7f, 0x01, 0xc3, 0x8b, 0x93, 0xe0, 0x89, 0x03, 0xe5, 0x95, 0x49, 0xa0,
	0xa0, 0x80, 0xe1, 0xf9, 0x24, 0x50, 0xc6, 0xa0, 0x58, 0x9f, 0x02, 0x45, 0xcc, 0xa4, 0x08, 0x14,
	0xe7, 0x05, 0x00, 0x80, 0x92, 0x08, 0xae, 0xef, 0x19, 0x5f, 0x68, 0x00, 0x77, 0xf7, 0xf7, 0x5d,
	0xd2, 0x73, 0xfe, 0xc1, 0x60, 0x1b, 0x73, 0x50, 0x8e, 0xac, 0xf0, 0x3d, 0xe3, 0xb1, 0x06, 0x70,
	0xdb, 0xa1, 0x77, 0x88, 0x7f, 0x7e, 0x10, 0x5c, 0x81, 0xbc, 0x67, 0x75, 0x1c, 0x69, 0xd7, 0x42,
	0xc0, 0x70, 0x59, 0x2a, 0xb0, 0x3a, 0x8e, 0x61, 0x72, 0x66, 0x18, 0x88, 0xf0, 0xff, 0x3d, 0xf2,
	0x99, 0xc0, 0x5d, 0x02, 0x1b, 0x8a, 0x13, 0x6a, 0x95, 0xcb, 0xa8, 0xa0, 0xe5, 0xcf, 0x58, 0xd0,
	0x8c, 0x1f, 0x0a, 0x50, 0x8e, 0xae, 0xe8, 0x7b, 0x68, 0x03, 0xca, 0x7b, 0xc3, 0xc1, 0xc0, 0xe9,
	0xd1, 0x9d, 0xd0, 0x4a, 0x4d, 0x58, 0x39, 0x62, 0x38, 0x4e, 0x36, 0xe3, 0x1b, 0xd4, 0x04, 0xa0,
	0x7d, 0x6a, 0xb9, 0x5b, 0xfd, 0x61, 0x8f, 0xca, 0x7b, 0xcd, 0x8f, 0x18, 0x8e, 0x51, 0xcd, 0xd8,
	0x1a, 0xdd, 0x82, 0xbc, 0x4b, 0x7c, 0x5a, 0xd5, 0xeb, 0x7a, 0xa3, 0xdc, 0x5a, 0x69, 0x4e, 0xf5,
	0xae, 0x66, 0xcc, 0xa0, 0x66, 0xb8, 0x68, 0x97, 0x46, 0x0c, 0x73, 0x21, 0x93, 0xff, 0xad, 0x7d,
	0x9d, 0x87, 0x7c, 0xc8, 0x88, 0x2a, 0x87, 0x96, 0xac, 0x1c, 0x08, 0x27, 0xdb, 0xcf, 0xec, 0x88,
	0x61, 0x41, 0x50, 0xe9, 0xda, 0x98, 0xea, 0x31, 0x95, 0x11, 0xc3, 0x11, 0x2d, 0x96, 0xa7, 0x37,
	0x60, 0x41, 0x54, 0xef, 0x8f, 0xa2, 0xe0, 0xcb, 0xfc, 0x1e, 0x31, 0x3c, 0xc9, 0x32, 0xe7, 0x05,
	0x61, 0x47, 0x85, 0xfb, 0x3a, 0x54, 0xe4, 0x91, 0xdd, 0x21, 0x71, 0x6d, 0x99, 0xe9, 0x8b, 0x23,
	0x86, 0x13, 0x74, 0xb3, 0x2c, 0x76, 0xed, 0x70, 0x83, 0x5e, 0x8e, 0x67, 0xba, 0x68, 0x34, 0x73,
	0x23, 0x86, 0xc7, 0xc4, 0x78, 0x8a, 0xaf, 0x8e, 0x53, 0x5c, 0x14, 0x85, 0xf2, 0x88, 0x61, 0x45,
	0x1a, 0xe7, 0xb6, 0x01, 0x45, 0x9f, 0x5a, 0x74, 0xe8, 0xf3, 0x42, 0xa0, 0xb7, 0x61, 0xc4, 0xb0,
	0xa4, 0x98, 0xf2, 0x7f, 0xcc, 0x58, 0x97, 0x74, 0x09, 0xe5, 0x35, 0x20, 0x69, 0x2c, 0xa7, 0x2b,
	0x63, 0xef, 0x84, 0x9b, 0xd0, 0x93, 0x51, 0xd1, 0x80, 0xb1, 0x27, 0x15, 0x2d, 0x56, 0x2d, 0x5e,
	0x9b, 0xa8, 0x16, 0xe5, 0xb1, 0xfa, 0x38, 0x3d, 0x59, 0x26, 0x54, 0xa4, 0xf8, 0x05, 0x2b, 0x5c,
	0x22, 0x8a, 0x14, 0xbf, 0x61, 0xb4, 0x32, 0xbe, 0x14, 0xe9, 0xfa, 0x5e, 0x6f, 0xbf, 0x7f, 0x9e,
	0x73, 0x8c, 0x08, 0x9c, 0xc0, 0x75, 0x6c, 0x8e, 0xe1, 0xe4, 0x8c, 0x39, 0x46, 0xf0, 0x7e, 0xd7,
	0x78, 0x6e, 0x09, 0x7b, 0x62, 0x6d, 0xee, 0x09, 0x82, 0xb5, 0x31, 0x35, 0x09, 0x65, 0x38, 0x6b,
	0x2a, 0x18, 0x85, 0xd3, 0x06, 0x23, 0x0a, 0x76, 0xf1, 0xa4, 0x60, 0x1b, 0xdf, 0x68, 0x70, 0xe9,
	0xb6, 0x43, 0xdb, 0x2e, 0xe9, 0x7d, 0xda, 0xb6, 0x7a, 0x3d, 0x67, 0x70, 0x21, 0x62, 0xf2, 0x79,
	0x0e, 0xd0, 0xa4, 0x59, 0xff, 0xb5, 0xd0, 0x3c, 0xd2, 0xa0, 0x12, 0xfa, 0xe0, 0xc2, 0x44, 0x25,
	0x6a, 0x5f, 0xfa, 0x59, 0xdb, 0xd7, 0x8f, 0x39, 0x98, 0x8b, 0x5d, 0xc5, 0xf7, 0xd0, 0x96, 0xec,
	0x2e, 0x1a, 0xef, 0x2e, 0xab, 0xe9, 0xdd, 0x65, 0x7c, 0x3e, 0xbd, 0xbf, 0xfc, 0xa6, 0x3d, 0xbd,
	0xfe, 0x72, 0x71, 0x70, 0xd1, 0xe2, 0xa9, 0xa1, 0x5a, 0x97, 0x9a, 0x7a, 0x5e, 0x90, 0xb1, 0x11,
	0x4e, 0xe0, 0x9e, 0x0a, 0xf7, 0x32, 0x00, 0x37, 0xe1, 0xf2, 0x94, 0x8c, 0xef, 0x85, 0x3f, 0x1a,
	0x43, 0x94, 0xae, 0x7e, 0x34, 0x6a, 0x93, 0xd1, 0xca, 0xf8, 0x5e, 0xe7, 0x60, 0x0c, 0x25, 0xb7,
	0x1f, 0xfc, 0x6b, 0xa6, 0x2c, 0xfd, 0x6c, 0xcf, 0xc6, 0xad, 0xe9, 0x19, 0x42, 0x3c, 0x3d, 0x6b,
	0x01, 0xc3, 0x57, 0xe2, 0xef, 0xc7, 0xe8, 0x80, 0x31, 0x35, 0x4a, 0xbc, 0x14, 0x75, 0x70, 0x11,
	0xc6, 0x4b, 0x01, 0xc3, 0x73, 0xd1, 0xf0, 0x4f, 0x87, 0xbe, 0x11, 0x35, 0xf2, 0x27, 0xfe, 0x54,
	0x30, 0x7e, 0x29, 0xf0, 0xc4, 0x53, 0x61, 0x7b, 0x3a, 0x93, 0xe3, 0x56, 0x62, 0x72, 0x5c, 0xcd,
	0x9e, 0x1c, 0x85, 0x49, 0xe9, 0xb9, 0xfd, 0xdd, 0xb3, 0xd9, 0xf1, 0xd9, 0xec, 0x38, 0x55, 0x5b,
	0x55, 0x95, 0x9b, 0xe3, 0x56, 0x4c, 0x54, 0xb9, 0xd6, 0x57, 0x79, 0x28, 0x99, 0xf2, 0x93, 0x1b,
	0xba, 0x01, 0xba, 0x65, 0xdb, 0xe8, 0xb9, 0x14, 0xf8, 0x89, 0x2f, 0x68, 0xb5, 0x5a, 0x16, 0xcb,
	0xf7, 0xd0, 0x5b, 0x50, 0xb0, 0x6c, 0x7b, 0xfb, 0xc1, 0x5f, 0x95, 0xbf, 0x09, 0x79, 0xc7, 0x26,
	0x14, 0xa5, 0x9d, 0x91, 0xdf, 0x4a, 0x6a, 0xcf, 0x67, 0xf2, 0x7c, 0x0f, 0xbd, 0x0b, 0x33, 0x7d,
	0xf1, 0xc6, 0x45, 0x57, 0x53, 0xce, 0x8d, 0x5f, 0xe1, 0xb5, 0xa5, 0x93, 0xd8, 0x42, 0x53, 0x47,
	0xe4, 0x5b, 0xaa, 0xa6, 0xf1, 0xcb, 0x39, 0x55, 0x53, 0xfc, 0xd5, 0xf9, 0x31, 0x2c, 0x74, 0x92,
	0x5d, 0x04, 0x65, 0x64, 0xf7, 0x44, 0x77, 0xaa, 0xad, 0x9d, 0xe6, 0x98, 0xef, 0xa1, 0xf7, 0x61,
	0xb6, 0xa3, 0x6a, 0x03, 0xc2, 0x27, 0x57, 0x8e, 0xc3, 0x5a, 0xfd, 0xcf, 0x4a, 0x4b, 0xcb, 0x84,
	0xe2, 0x3d, 0xcf, 0xb5, 0xfc, 0x03, 0xe9, 0x85, 0x70, 0xc8, 0xcf, 0xf2, 0x82, 0x7c, 0x90, 0x64,
	0x79, 0x41, 0xbd, 0x0f, 0x5a, 0xdf, 0x6a, 0x50, 0x14, 0x93, 0x09, 0xba, 0x0b, 0xf3, 0x9d, 0xc4,
	0x94, 0xfa, 0x37, 0x75, 0xcb, 0xfb, 0x4b, 0x5d, 0xf8, 0xe4, 0xa9, 0x28, 0xf3, 0xfe, 0xe3, 0xb1,
	0xa9, 0xbd, 0xf8, 0xe8, 0x78, 0x49, 0xfb, 0xe9, 0x78, 0x49, 0x7b, 0x7c, 0xbc, 0xa4, 0x7d, 0x98,
	0x3b, 0xda, 0xd8, 0x2d, 0xf2, 0x0f, 0xc9, 0xd7, 0xff, 0x08, 0x00, 0x00, 0xff, 0xff, 0xf2, 0x3d,
	0xb2, 0x2a, 0x9c, 0x16, 0x00, 0x00,
}
