package dao

import (
	"go-common/app/admin/ep/merlin/model"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

var (
	username = "<EMAIL>"
)

func Test_Mail_Log(t *testing.T) {
	Convey("test add mail log", t, func() {
		ml := &model.MailLog{
			ReceiverName: username,
			MailType:     1,
			SendContext:  "test add mail log",
		}
		err := d.InsertMailLog(ml)
		So(err, ShouldBeNil)
	})

	<PERSON>vey("test find mail log", t, func() {
		mailLogs, err := d.<PERSON><PERSON>ail<PERSON>og(username)
		So(len(mailLogs), ShouldBeGreaterThan, 0)
		So(err, ShouldBeNil)
	})

	<PERSON>vey("test delete mail log", t, func() {
		err := d.<PERSON>(username)
		So(err, ShouldBeNil)
	})

	<PERSON>vey("test find mail log", t, func() {
		mailLogs, err := d.<PERSON><PERSON><PERSON><PERSON><PERSON>(username)
		So(len(mailLogs), ShouldEqual, 0)
		So(err, ShouldBeNil)
	})
}
