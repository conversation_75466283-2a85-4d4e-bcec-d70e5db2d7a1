package model

// <PERSON><PERSON><PERSON><PERSON> def
type Pair<PERSON>ey struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

// TeamInfoResp def
type TeamInfoResp struct {
	Department []*PairKey `json:"department"`
	Business   []*Pair<PERSON>ey `json:"business"`
}

// Developer def
type Developer struct {
	Department string `json:"department"`
	Total      int    `json:"total"`
	Android    int    `json:"android"`
	Ios        int    `json:"ios"`
	Web        int    `json:"web"`
	Service    int    `json:"service"`
	Other      int    `json:"other"`
}
