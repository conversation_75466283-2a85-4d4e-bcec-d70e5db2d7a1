package http

import (
	"go-common/app/admin/ep/melloi/model"
	bm "go-common/library/net/http/blademaster"
	"go-common/library/net/http/blademaster/binding"
)

// get administrator for order by current username
func queryOrderAdmin(c *bm.Context) {
	userName, _ := c.Request.Cookie("username")
	c.<PERSON>(srv.QueryOrderAdmin(userName.Value))
}

// add administrator for order
func addOrderAdmin(c *bm.Context) {
	admin := model.OrderAdmin{}
	if err := c.Bind<PERSON>ith(&admin, binding.Form); err != nil {
		return
	}
	c.<PERSON>(nil, srv.AddOrderAdmin(&admin))
}
